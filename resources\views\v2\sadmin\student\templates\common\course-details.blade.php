<script id="studCourseTemplate" type="text/html">
    <x-v2.animation.slide
        class="inline-flex items-start justify-start p-6 bg-white border rounded-md border-gray-200 w-full">
        <div class="inline-flex flex-col space-y-4 items-start justify-start w-full">
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <div class="inline-flex space-x-1 items-center justify-start w-48">
                    <p class="text-sm font-medium leading-5 text-gray-700">GENERAL</p>
                    <input type="hidden" name="student_id" value="{{ (isset($studentId)) ? $studentId : '' }}"
                        id="student_id" />
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 w-full border-b pb-2 gap-3 md:gap-2">
                    # bgcolor = 'gray'; textcolor = 'gray';

                    if (arr.status == 'Current Student'){
                    bgcolor = 'primary-blue';
                    textcolor = 'primary-blue';
                    } else if (arr.status == 'Cancelled'){
                    bgcolor = 'red';
                    textcolor = 'red';
                    } else if (arr.status == 'Transitioned'){
                    bgcolor = 'yellow';
                    textcolor = 'yellow';
                    } else if (arr.status == 'Completed'){
                    bgcolor = 'green';
                    textcolor = 'green';
                    } else if (arr.status == 'Finished'){
                    bgcolor = 'green';
                    textcolor = 'green';
                    } else if (arr.status == 'Withdrawn'){
                    bgcolor = 'pink';
                    textcolor = 'pink';
                    }else if(arr.status == 'Suspended'){
                    bgcolor = 'red';
                    textcolor = 'red';
                    } #
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Status</p>
                        <div
                            class="inline-flex items-center justify-center px-2.5 leading-6 bg-#=bgcolor #-100 rounded-full truncate w-fit">
                            <span class="text-xs text-center text-#= textcolor #-800 w-full">#=
                                displayOrDefault(arr.status) #</span>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Campus</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.campus_name)
                            #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Course Code</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.course_code)
                            #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Course</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2 w-full">#=
                            displayOrDefault(arr.course_name) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Offer No</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.offer_id) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        # let coe_name = (typeof(arr.coe_name) != "undefined" && arr.coe_name !== null) ? arr.coe_name :
                        'NA'; #
                        <p class="text-sm font-medium leading-4 text-gray-500">COE</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(coe_name) #</p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Agent</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2 w-full">#=
                            displayOrDefault(arr.agency_name) #</p>
                    </div>
                </div>
            </div>
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <div class="inline-flex space-x-1 items-center justify-start w-48">
                    <p class="text-xs font-medium leading-normal text-gray-700">DATES</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 w-full border-b pb-2 gap-3 md:gap-2">
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500">Intake</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.intake_year) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500 truncate w-full">Applied Date</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.applied_date) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500 truncate w-full">Offer issued Date</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.issued_date) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500 truncate w-full">Course Start Date</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.start_date) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500 truncate w-full">Exp. Course Finish Date
                        </p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.finish_date) #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500 truncate w-full">Study Duration</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            displayOrDefault(arr.course_duration)
                            # #= arr.course_duration_type #
                        </p>
                    </div>
                    #if(arr.old_finish_date != ''){#
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        <p class="text-sm font-medium leading-4 text-gray-500 truncate w-full">Previous Finish Date</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">
                            #= displayOrDefault(arr.old_finish_date) #
                        </p>
                    </div>
                    #}#
                </div>
            </div>
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <div class="inline-flex space-x-1 items-center justify-start w-48">
                    <p class="text-xs font-medium leading-normal text-gray-700">FEES</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 w-full border-b pb-2 gap-3 md:gap-2">
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        # let tuition_fee = (typeof(arr.course_fee) != "undefined" && arr.course_fee !== null) ?
                        arr.course_fee : 'NA'; #
                        <p class="text-sm font-medium leading-4 text-gray-500 w-full">Tuition standard fee</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            kendo.toString(tuition_fee, "c") #
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        # let enroll_fee = (typeof(arr.enroll_fee) != "undefined" && arr.enroll_fee !== null) ?
                        arr.enroll_fee : 'NA'; #
                        <p class="text-sm font-medium leading-4 text-gray-500 w-full">Enrollment fee</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            kendo.toString(enroll_fee, "c") #</p>
                    </div>
                    <div class="grid grid-cols-2 gap-2 md:block md:space-y-1">
                        # let course_upfront_fee = (typeof(arr.course_upfront_fee) != "undefined" &&
                        arr.course_upfront_fee !== null) ? arr.course_upfront_fee : 'NA'; #
                        <p class="text-sm font-medium leading-4 text-gray-500 w-full">Upfront Fee</p>
                        <p class="text-sm md:text-[13px] leading-5 text-gray-700 line-clamp-2">#=
                            kendo.toString(course_upfront_fee,
                            "c") #</p>
                    </div>
                </div>
            </div>
        </div>
    </x-v2.animation.slide>
    <x-v2.skeleton.templates.course-part type="both" class="tw-skeleton" style="display: none;" />
</script>