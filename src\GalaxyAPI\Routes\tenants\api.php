<?php

use GalaxyAPI\Controllers\CourseTypeController;
use GalaxyAPI\Controllers\PdfTemplateController;
use GalaxyAPI\Controllers\RegisterImprovementController;
use GalaxyAPI\Controllers\StaffController;
use GalaxyAPI\Controllers\StaffPositionController;
use GalaxyAPI\Controllers\StudentController;
use GalaxyAPI\Controllers\USIVerificationController;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
 * Documentation:
 * Supported routes:
    Route::get('', 'index');
    Route::post('', 'store');
    Route::post('delete', 'delete');
    Route::get('{id}', 'show');
    Route::post('{id}', 'update');
    Route::put('{id}/status-change/{column}', 'changeStatusOtherColumn');
    Route::put('{id}/status-change', 'changeStatus');
    Route::delete('{id}', 'destroy');
*/

Route::middleware([PreventAccessFromCentralDomains::class])
    ->group(function () {
        Route::prefix('students')
            ->controller(StudentController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('export/usi-students', 'exportUSIStudents');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
            });
        Route::prefix('course-types')
            ->controller(CourseTypeController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('{id}', 'show');
            });

        Route::prefix('usi-verifications')
            ->controller(USIVerificationController::class)
            ->group(function () {
                Route::post('verify', 'verify');
                Route::post('verify-bulk', 'verifyBulk');
            });

        Route::prefix('pdf-templates')
            ->controller(PdfTemplateController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
                Route::post('{id}/update-default', 'updateDefaultTemplate');
            });

        Route::prefix('register-improvement')->controller(RegisterImprovementController::class)->group(function () {
            Route::get('', 'index');
            Route::get('form-constants', 'formConstants');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('{id}', 'update');
            Route::post('delete', 'delete');
        });

        Route::prefix('staff')
            ->controller(StaffController::class)
            ->group(function () {
                Route::get('', 'index');
            });

        Route::prefix('staff-position')
            ->controller(StaffPositionController::class)
            ->group(function () {
                Route::get('', 'index');
            });
    });
