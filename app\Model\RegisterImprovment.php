<?php

namespace App\Model;

use App\Model\v2\Student;
use Auth;
use DB;
use Illuminate\Database\Eloquent\Model;

class RegisterImprovment extends Model
{
    //
    protected $table = 'rto_register_improvment';

    public function getCategoryList()
    {
        $arrCategory = RegisterImprovment::pluck('category', 'category')->toArray();
        $selCategory[''] = '- - Select Category - -';
        $returnStaffRecord = $selCategory + $arrCategory;
        $returnStaffRecord[0] = 'Add New Category';

        return $returnStaffRecord;
    }

    public function registerImprovmentAdd($request)
    {
        $college_id = Auth::user()->college_id;

        $objregisterImprovment = new RegisterImprovment;
        $objregisterImprovment->college_id = $college_id;
        $objregisterImprovment->user_type = ($request->input('user_type') != '') ? $request->input('user_type') : null;
        $objregisterImprovment->staff_role = ($request->input('staff_role') != '') ? $request->input('staff_role') : null;
        $objregisterImprovment->category = ($request->input('category') != '') ? $request->input('category') : null;
        $objregisterImprovment->case_detail = ($request->input('case_detail') != '') ? $request->input('case_detail') : null;
        $objregisterImprovment->action_taken = ($request->input('action_taken') != '') ? $request->input('action_taken') : null;
        $objregisterImprovment->action_taken_prevent = ($request->input('action_taken_prevent') != '') ? $request->input('action_taken_prevent') : null;
        $objregisterImprovment->status = ($request->input('status') != '') ? $request->input('status') : null;
        $objregisterImprovment->escalated_to = ($request->input('escalated_to') != '') ? $request->input('escalated_to') : null;
        $objregisterImprovment->logged_by = Auth::user()->id;
        $objregisterImprovment->lodged_date = ($request->input('lodged_date') != '') ? date('Y-m-d', strtotime($request->input('lodged_date'))) : null;
        if (! empty($request->input('requested_by_stu'))) {
            $objregisterImprovment->requested_by = ($request->input('requested_by_stu') != '') ? $request->input('requested_by_stu') : null;
        } else {
            $objregisterImprovment->requested_by = ($request->input('requested_by') != '') ? $request->input('requested_by') : null;
        }
        if (! empty($request->input('category'))) {
            $objregisterImprovment->category = ($request->input('category') != '') ? $request->input('category') : null;
        } else {
            $objregisterImprovment->category = ($request->input('category_add') != '') ? $request->input('category_add') : null;
        }

        $objregisterImprovment->created_by = Auth::user()->id;
        $objregisterImprovment->updated_by = Auth::user()->id;
        $objregisterImprovment->save();
    }

    public function registerImprovmentEdit($registerId, $request)
    {

        $college_id = Auth::user()->college_id;
        $objregisterImprovment = RegisterImprovment::find($registerId);
        $objregisterImprovment->college_id = $college_id;
        $objregisterImprovment->user_type = ($request->input('user_type') != '') ? $request->input('user_type') : null;
        $objregisterImprovment->staff_role = ($request->input('staff_role') != '') ? $request->input('staff_role') : null;
        $objregisterImprovment->action_taken = ($request->input('action_taken') != '') ? $request->input('action_taken') : null;
        $objregisterImprovment->category = ($request->input('category1') != '') ? $request->input('category1') : null;
        $objregisterImprovment->action_taken_prevent = ($request->input('action_taken_prevent') != '') ? $request->input('action_taken_prevent') : null;
        $objregisterImprovment->status = ($request->input('status') != '') ? $request->input('status') : null;
        $objregisterImprovment->escalated_to = ($request->input('escalated_to') != '') ? $request->input('escalated_to') : null;
        $objregisterImprovment->logged_by = ($request->input('logged_by') != '') ? $request->input('logged_by') : null;
        $objregisterImprovment->lodged_date = ($request->input('lodged_date') != '') ? date('Y-m-d', strtotime($request->input('lodged_date'))) : null;
        if (! empty($request->input('requested_by_stu')) && $request->input('user_type') == 1) {
            $objregisterImprovment->requested_by = ($request->input('requested_by') != '') ? $request->input('requested_by') : null;
        }
        if (! empty($request->input('requested_by')) && $request->input('user_type') == 2) {
            $objregisterImprovment->requested_by = ($request->input('requested_by_stu') != '') ? $request->input('requested_by_stu') : null;
        }

        $objregisterImprovment->created_by = Auth::user()->id;
        $objregisterImprovment->updated_by = Auth::user()->id;
        $objregisterImprovment->save();
    }

    public function registerImprovmentView($perPage)
    {

        $college_id = Auth::user()->college_id;
        $arrregisterImprovment = RegisterImprovment::leftjoin('rto_staff_and_teacher as staff', 'staff.id', '=', 'rto_register_improvment.staff_role')
            ->leftjoin('rto_staff_and_teacher as staff_req', 'staff_req.id', '=', 'rto_register_improvment.requested_by')
            ->leftjoin('rto_students as stu_req', 'stu_req.generated_stud_id', '=', 'rto_register_improvment.requested_by')
            ->select('rto_register_improvment.*', DB::raw("CONCAT(stu_req.first_name,' ',stu_req.family_name) AS student"), 'staff.first_name as first_name', 'staff_req.first_name as first_name_req')
            ->where('rto_register_improvment.college_id', $college_id)
            ->orderBy('id', 'desc')
            ->paginate($perPage);

        return $arrregisterImprovment;
    }

    public function dataRegisterImprovment($registerId)
    {
        return $arrRegisterImprovment = RegisterImprovment::where('college_id', '=', Auth::user()->college_id)->where('id', '=', $registerId)->get();
    }

    public function getRegisterFilterData($filterId)
    {

        if ($filterId == 'category') {
            $arrRegisterImprovment = RegisterImprovment::where('college_id', '=', Auth::user()->college_id)
                ->groupBy($filterId)
                ->get()->pluck('category', 'category')->toArray();
        } elseif ($filterId == 'requested_by') {

            $arrRegisterImprovment = RegisterImprovment::join('rto_staff_and_teacher as staff_req', 'staff_req.id', '=', 'rto_register_improvment.requested_by')
                ->select('rto_register_improvment.*', 'staff_req.first_name as first_name_req')
                ->where('rto_register_improvment.college_id', '=', Auth::user()->college_id)
                ->groupBy($filterId)
                ->get()->pluck('first_name_req', 'requested_by')->toArray();
        } elseif ($filterId == 'lodged_person') {
            $arrRegisterImprovment = RegisterImprovment::join('rto_users as staff_req', 'staff_req.id', '=', 'rto_register_improvment.logged_by')
                ->select('rto_register_improvment.*', 'staff_req.name as user_name')
                ->where('rto_register_improvment.college_id', '=', Auth::user()->college_id)
                ->groupBy('logged_by')
                ->get()->pluck('user_name', 'logged_by')->toArray();
        } elseif ($filterId == 'lodged_date') {
            $arrRegisterImprovment = RegisterImprovment::where('college_id', '=', Auth::user()->college_id)
                ->groupBy($filterId)
                ->select(DB::raw("DATE_FORMAT(rto_register_improvment.lodged_date, '%Y-%m-%d') AS createdAt"), DB::raw("DATE_FORMAT(rto_register_improvment.lodged_date, '%Y-%m-%d') AS createdAts"))
                ->pluck('createdAt', 'createdAts')->toArray();
        } elseif ($filterId == 'status') {
            $arrRegisterImprovment['0'] = 'Case Close';
            $arrRegisterImprovment['1'] = 'Case Open';
        } else {
            $arrRegisterImprovment = RegisterImprovment::where('college_id', '=', Auth::user()->college_id)
                ->groupBy($filterId)
                ->get();
        }
        $selCategory[''] = '- - Select Option - -';
        $returnFilterArray = $selCategory + $arrRegisterImprovment;

        return $returnFilterArray;
    }

    public function registerImprovmentFilter($filterId)
    {

        if ($filterId == 'requested_by') {
            return $arrregisterImprovment = RegisterImprovment::join('rto_staff_and_teacher as staff_req', 'staff_req.id', '=', 'rto_register_improvment.requested_by')
                ->select('rto_register_improvment.*', 'staff_req.first_name as first_name_req')
                ->where('rto_register_improvment.college_id', '=', Auth::user()->college_id)
                ->groupBy($filterId)
                ->get();
        } elseif ($filterId == 'lodged_person') {
            return $arrregisterImprovment = RegisterImprovment::join('rto_users as staff_req', 'staff_req.id', '=', 'rto_register_improvment.logged_by')
                ->select('rto_register_improvment.*', 'staff_req.name as user_name')
                ->where('rto_register_improvment.college_id', '=', Auth::user()->college_id)
                ->groupBy('logged_by')
                ->get();
        } else {
            return $arrregisterImprovment = RegisterImprovment::where('college_id', '=', Auth::user()->college_id)
                ->groupBy($filterId)
                ->get();
        }
    }

    public function registerImprovmentFilterData($dropDownId, $searchValue, $finishDate, $startDate)
    {

        // return  $arrRegisterImprovment = RegisterImprovment::where('college_id', '=', Auth::user()->college_id)->where($dropDownId,'=',$searchValue)->get();
        $college_id = Auth::user()->college_id;
        if ($dropDownId == 'anyword') {
            return RegisterImprovment::leftjoin('rto_staff_and_teacher as staff', 'staff.id', '=', 'rto_register_improvment.staff_role')
                ->leftjoin('rto_staff_and_teacher as staff_req', 'staff_req.id', '=', 'rto_register_improvment.requested_by')
                //  ->join('rto_students as stu_req', 'stu_req.student_id', '=', 'rto_register_improvment.requested_by_student')
                ->select(['rto_register_improvment.*', 'staff.first_name as first_name', 'staff_req.first_name as first_name_req', DB::raw('DATE_FORMAT(rto_register_improvment.created_at,"%d %m %Y") as created')])
                ->where('rto_register_improvment.college_id', $college_id)
                ->where(function ($query) use ($searchValue) {
                    return $query
                        ->where('rto_register_improvment.action_taken', 'LIKE', '%'.$searchValue.'%')
                        ->orwhere('rto_register_improvment.action_taken_prevent', 'LIKE', '%'.$searchValue.'%')
                        ->orwhere('rto_register_improvment.case_detail', 'LIKE', '%'.$searchValue.'%');
                })
                ->get();
        }
        if ($dropDownId == 'between_dates') {
            $form_date = date('Y-m-d', strtotime($startDate));
            $to_date = date('Y-m-d', strtotime($finishDate));

            return RegisterImprovment::leftjoin('rto_staff_and_teacher as staff', 'staff.id', '=', 'rto_register_improvment.staff_role')
                ->leftjoin('rto_staff_and_teacher as staff_req', 'staff_req.id', '=', 'rto_register_improvment.requested_by')
                //  ->join('rto_students as stu_req', 'stu_req.student_id', '=', 'rto_register_improvment.requested_by_student')
                ->select(['rto_register_improvment.*', 'staff.first_name as first_name', 'staff_req.first_name as first_name_req', DB::raw('DATE_FORMAT(rto_register_improvment.created_at,"%d %m %Y") as created')])
                ->where('rto_register_improvment.college_id', $college_id)
                ->whereBetween('rto_register_improvment.created_at', [$form_date, $to_date])
                //                            ->whereBetween('rto_register_improvment.created_atf', array($startDate, $finishDate))
                ->get();
        }

        return $arrregisterImprovment = RegisterImprovment::leftjoin('rto_staff_and_teacher as staff', 'staff.id', '=', 'rto_register_improvment.staff_role')
            ->leftjoin('rto_staff_and_teacher as staff_req', 'staff_req.id', '=', 'rto_register_improvment.requested_by')
            //  ->join('rto_students as stu_req', 'stu_req.student_id', '=', 'rto_register_improvment.requested_by_student')
            ->select(['rto_register_improvment.*', 'staff.first_name as first_name', 'staff_req.first_name as first_name_req', DB::raw('DATE_FORMAT(rto_register_improvment.lodged_date,"%d %M %Y") as created')])
            ->where('rto_register_improvment.college_id', $college_id)
            ->where($dropDownId, '=', $searchValue)
            ->get();
    }

    public function deleteRegister($registerId)
    {

        return RegisterImprovment::where('id', '=', $registerId)->delete();
    }

    public function staffUser()
    {
        return $this->belongsTo(Staff::class, 'requested_by');
    }

    public function studentUser()
    {
        return $this->belongsTo(Student::class, 'requested_by', 'generated_stud_id');
    }
}
