<template>
    <fieldwrapper>
        <klabel
            :class="'k-form-label'"
            :editor-id="id"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
            class="mb-1 font-medium leading-5 text-gray-700"
            v-if="label"
        >
            {{ label }}
            <InputLabelInfo :info="labelinfo" />
        </klabel>
        <div class="k-form-field-wrap">
            <!--
                <numerictextbox 
                :name="'age'"
                :label="'Age'"
                :required="true"
            ></numerictextbox>-->
            <numerictextbox
                :valid="valid"
                :id="id"
                :value="value"
                :disabled="disabled"
                :spinners="spinners"
                :step="step"
                :min="min"
                :max="max"
                :placeholder="placeholder"
                :format="format"
                @change="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
            ></numerictextbox>

            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else>{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { NumericTextBox } from '@progress/kendo-vue-inputs';
import InputLabelInfo from '@spa/components/inputLabelInfo.vue';

export default {
    props: {
        modelValue: Number,
        optional: Boolean,
        disabled: Boolean,
        spinners: Boolean,
        step: {
            type: [Number],
            default: 1,
        },
        min: [null, Number],
        max: [null, Number],
        placeholder: String,
        touched: Boolean,
        label: String,
        labelinfo: {
            type: Object,
            default: {},
        },
        validationMessage: String,
        hint: String,
        id: String,
        valid: Boolean,
        format: { type: String, default: 'n' },
        value: {
            type: [Number, String],
            default: null,
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        numerictextbox: NumericTextBox,
        InputLabelInfo,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
    },
    emits: {
        input: null,
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    methods: {
        handleInput(e) {
            this.$emit('input', e);
        },
        handleChange(e) {
            this.$emit('input', e);
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('change', e);
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
