<template>
    <div>
        <fieldwrapper :class="rootClass">
            <klabel
                :editor-id="id"
                :editor-valid="valid"
                :disabled="disabled"
                :optional="optional"
                :class="labelClass"
            >
                {{ label }}
                <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
                <i class="block text-[8px] text-gray-500" v-if="helperText"> [{{ helperText }}] </i>
            </klabel>
            <div :class="wrapClass">
                <datepicker
                    :format="format"
                    :formatPlaceholder="formatPlaceholder"
                    :valid="valid"
                    :id="id"
                    :disabled="disabled"
                    :placeholder="placeholder"
                    :min="min"
                    :max="max"
                    :default-value="getDefaultValue"
                    @change="handleChange"
                    @input="handleInput"
                    @blur="handleBlur"
                    @focus="handleFocus"
                    :class="fieldClass"
                />
                <error v-if="showValidationMessage">
                    {{ validationMessage }}
                </error>
                <hint v-else>{{ hint }}</hint>
            </div>
        </fieldwrapper>
    </div>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { DatePicker } from '@progress/kendo-vue-dateinputs';
import { twMerge } from 'tailwind-merge';
export default {
    props: {
        modelValue: [Date, String],
        optional: Boolean,
        disabled: Boolean,
        placeholder: {
            type: String,
            default: 'dd-mm-yyyy',
        },
        touched: Boolean,
        label: String,
        format: { type: String, default: 'dd-MM-yyyy' },
        validationMessage: String,
        hint: String,
        helperText: String,
        id: String,
        valid: Boolean,
        defaultValue: {
            type: [null, Date],
            default: null,
        },
        min: {
            type: Date,
            default: new Date('1900'),
        },
        max: {
            type: Date,
            default: new Date(new Date().setFullYear(new Date().getFullYear() + 100)),
        },
        value: {
            type: [Date, String],
            default: null,
        },
        pt: {
            type: Object,
            default: {},
        },
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        formatPlaceholder: {
            type: Object,
            default: {
                day: 'dd',
                month: 'mm',
                year: 'yyyy',
            },
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        datepicker: DatePicker,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        getDefaultValue() {
            const parsedDate = new Date(this.value);
            if (
                this.value &&
                !isNaN(parsedDate.getTime()) &&
                parsedDate >= this.min &&
                parsedDate <= this.max
            ) {
                // If this.value is a valid Date and falls within the min and max range, return it
                return parsedDate;
            } else {
                // If this.value is not a valid Date or is out of range, return defaultValue
                return this.defaultValue;
            }
        },
        fieldClass() {
            return twMerge('tw-input__datepicker', this.pt.field);
        },
        rootClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('tw-form__fieldwrapper field-horizontal', this.pt.root);
            }
            return twMerge('tw-form__fieldwrapper', this.pt.root);
        },
        wrapClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('k-form-field-wrap', this.pt.wrap);
            }
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            if (this.orientation == 'horizontal') {
                return twMerge(
                    'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                    this.pt.label
                );
            }
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
    },
    emits: {
        input: null,
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    methods: {
        handleChange(e) {
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleInput(e) {
            this.$emit('input', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
