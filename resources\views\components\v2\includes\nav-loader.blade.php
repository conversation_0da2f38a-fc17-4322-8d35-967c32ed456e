<div id="global-loader"
    class="hidden fixed top-16 right-0 bottom-0 bg-white bg-opacity-80 flex items-center justify-center z-50 left-16">
    <x-v2.loader :showText="false" />
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const links = document.querySelectorAll('a.tw-menu-link, .tw-sidebar a');
        let loaderTimeout;

        links.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                const isActive = link.classList.contains('active');
                
                if (href === '#' || isActive) return;
                showLoader();
                setTimeout(() => {
                    hideLoader();
                }, 2000);
            });
        });

        window.addEventListener('beforeunload', () => {
            clearTimeout(loaderTimeout);
        });

        // Detect browser back/forward (popstate)
        window.addEventListener('popstate', () => {
            // showLoader();
        });
        
        // Monkey-patch pushState and replaceState to detect location change
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        function hookHistoryMethod(method) {
            return function (...args) {
                const result = method.apply(this, args);
                const event = new Event('locationchange');
                window.dispatchEvent(event);
                return result;
            };
        }
        
        history.pushState = hookHistoryMethod(originalPushState);
        history.replaceState = hookHistoryMethod(originalReplaceState);
        
        window.addEventListener('locationchange', () => {
            console.log("locationChange")
        // showLoader();
        });

        function showLoader() {
            const loaderWrapper = document.getElementById('global-loader');
            const loaderInner = document.getElementById('loader-inner');
            const sidebarExpanded = getCookie('_x_isSidebarExpanded') === 'true';
            
            if (loaderWrapper) {
                loaderWrapper.classList.remove('left-16', 'left-64');
                loaderWrapper.classList.add(sidebarExpanded ? 'left-64' : 'left-16');
                loaderWrapper.classList.remove('hidden');
            }
            
            clearTimeout(loaderTimeout);
                loaderTimeout = setTimeout(() => {
                if (loaderInner) loaderInner.classList.remove('hidden');
            }, 500);
        }

        function hideLoader() {
            const loaderWrapper = document.getElementById('global-loader');
            const loaderInner = document.getElementById('loader-inner');
            if (loaderWrapper) loaderWrapper.classList.add('hidden');
            if (loaderInner) loaderInner.classList.add('hidden');
        }

        function getCookie(name) {
            const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
            return match ? decodeURIComponent(match[2]) : null;
        }
    });
</script>