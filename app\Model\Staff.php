<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Staff extends Model
{
    protected $table = 'rto_staff_and_teacher';

    public function staffAdd($lastUserId, $request)
    {
        $objStaff = new Staff;
        $objStaff->college_id = Auth::user()->college_id;
        $objStaff->user_id = $lastUserId;
        $objStaff->name_title = ($request->input('title') != '') ? $request->input('title') : null;
        $objStaff->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $objStaff->last_name = ($request->input('last_name') != '') ? $request->input('last_name') : null;
        $objStaff->country = ($request->input('country') != '') ? $request->input('country') : null;
        $objStaff->address = ($request->input('Address') != '') ? $request->input('Address') : null;
        $objStaff->city_town = ($request->input('city_town') != '') ? $request->input('city_town') : null;
        $objStaff->state = ($request->input('state') != '') ? $request->input('state') : null;
        $objStaff->postcode = ($request->input('postcode') != '') ? $request->input('postcode') : null;
        $objStaff->phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $objStaff->mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $objStaff->email = ($request->input('email') != '') ? $request->input('email') : null;
        $objStaff->signatory_text = ($request->input('signatory_text') != '') ? $request->input('signatory_text') : null;

        $objStaff->staff_number = ($request->input('staffnumber') != '') ? $request->input('staffnumber') : null;
        $objStaff->position = ($request->input('position') != '') ? $request->input('position') : null;
        $objStaff->staff_type = 'Staff';
        $objStaff->is_active = ($request->input('status') != '') ? $request->input('status') : null;

        $objStaff->birth_date = ($request->input('birth_date') != '') ? date('Y-m-d', strtotime($request->input('birth_date'))) : null;
        $objStaff->joining_date = ($request->input('joining_date') != '') ? date('Y-m-d', strtotime($request->input('joining_date'))) : null;
        $objStaff->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $objStaff->highest_qualification_code = ($request->input('highest_qualification_code') != '') ? $request->input('highest_qualification_code') : null;
        $objStaff->highest_qualification_place_code = ($request->input('highest_qualification_place_code') != '') ? $request->input('highest_qualification_place_code') : null;
        $objStaff->work_contract_code = ($request->input('work_contract_code') != '') ? $request->input('work_contract_code') : null;
        $objStaff->staff_work_level_code = ($request->input('staff_work_level_code') != '') ? $request->input('staff_work_level_code') : null;
        $objStaff->organisational_unit_code = ($request->input('organisational_unit_code') != '') ? $request->input('organisational_unit_code') : null;
        $objStaff->work_sector_code = ($request->input('work_sector_code') != '') ? $request->input('work_sector_code') : null;
        $objStaff->function_code = ($request->input('function_code') != '') ? $request->input('function_code') : null;

        $objStaff->created_by = Auth::user()->id;
        $objStaff->updated_by = Auth::user()->id;

        if ($objStaff->save()) {
            return $objStaff->id;
        }
    }

    public function staffEdit($request)
    {

        $staffId = $request->id;
        $objStaff = Staff::find($staffId);
        $objStaff->name_title = ($request->input('title') != '') ? $request->input('title') : null;
        $objStaff->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $objStaff->last_name = ($request->input('last_name') != '') ? $request->input('last_name') : null;
        $objStaff->country = ($request->input('country') != '') ? $request->input('country') : null;
        $objStaff->address = ($request->input('Address') != '') ? $request->input('Address') : null;
        $objStaff->city_town = ($request->input('city_town') != '') ? $request->input('city_town') : null;
        $objStaff->state = ($request->input('state') != '') ? $request->input('state') : null;
        $objStaff->postcode = ($request->input('postcode') != '') ? $request->input('postcode') : null;
        $objStaff->phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $objStaff->mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $objStaff->email = ($request->input('email') != '') ? $request->input('email') : null;
        $objStaff->signatory_text = ($request->input('signatory_text') != '') ? $request->input('signatory_text') : null;
        $objStaff->staff_number = ($request->input('staffnumber') != '') ? $request->input('staffnumber') : null;
        $objStaff->position = ($request->input('position') != '') ? $request->input('position') : null;
        $objStaff->is_active = ($request->input('status') != '') ? $request->input('status') : null;

        $objStaff->birth_date = ($request->input('birth_date') != '') ? date('Y-m-d', strtotime($request->input('birth_date'))) : null;
        $objStaff->joining_date = ($request->input('joining_date') != '') ? date('Y-m-d', strtotime($request->input('joining_date'))) : null;
        $objStaff->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $objStaff->highest_qualification_code = ($request->input('highest_qualification_code') != '') ? $request->input('highest_qualification_code') : null;
        $objStaff->highest_qualification_place_code = ($request->input('highest_qualification_place_code') != '') ? $request->input('highest_qualification_place_code') : null;
        $objStaff->work_contract_code = ($request->input('work_contract_code') != '') ? $request->input('work_contract_code') : null;
        $objStaff->staff_work_level_code = ($request->input('staff_work_level_code') != '') ? $request->input('staff_work_level_code') : null;
        $objStaff->organisational_unit_code = ($request->input('organisational_unit_code') != '') ? $request->input('organisational_unit_code') : null;
        $objStaff->work_sector_code = ($request->input('work_sector_code') != '') ? $request->input('work_sector_code') : null;
        $objStaff->function_code = ($request->input('function_code') != '') ? $request->input('function_code') : null;
        $objStaff->atsi_code = ($request->input('atsi_code') != '') ? $request->input('atsi_code') : null;

        $objStaff->updated_by = Auth::user()->id;
        $objStaff->save();

        return $objStaff->user_id;
    }

    public function getStaffData($perPage)
    {
        // return Staff::where('college_id', '=', Auth::user()->college_id)->get();
        return Staff::leftjoin('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', Auth::user()->college_id)
            ->orderBy('rto_staff_and_teacher.id', 'DESC')
            ->select('rto_users.username', 'rto_staff_and_teacher.*')
            ->paginate($perPage);
    }

    // get staff teacher datatable data
    public function getStaffDatatable($collegeId, $request)
    {
        $requestData = $_REQUEST;
        $columns = [
            0 => 'rto_staff_and_teacher.staff_number',
            1 => 'rto_staff_and_teacher.position',
            2 => 'rto_staff_and_teacher.first_name',
            3 => 'rto_users.username',
            4 => 'rto_staff_and_teacher.email',
            5 => 'rto_staff_and_teacher.is_active',
            6 => 'rto_staff_and_teacher.address',
            7 => 'rto_staff_and_teacher.city_town',
        ];

        $query = Staff::join('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', $collegeId);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 5 && strtolower($searchVal) == 'active') {
                        $searchVal = 1;
                    } elseif ($key == 5 && strtolower($searchVal) == 'inactive') {
                        $searchVal = 0;
                    }
                    $results = [];
                    if ($key == 1 && (strtolower($searchVal) == 'staff teacher' || strtolower($searchVal) == 'teacher')) {
                        $results = [7];
                    } elseif ($key == 1 && (strtolower($searchVal) == 'staff')) {
                        $results = [1, 2, 3, 4, 5, 6, 8];
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } elseif (! empty($results) && $key == 1) {
                            $query->orWhere(function ($subquery) use ($value, $results) {
                                $subquery->whereIn($value, $results);
                            });
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rto_staff_and_teacher.staff_number', 'rto_staff_and_teacher.position', 'rto_staff_and_teacher.address', 'rto_users.username', 'rto_staff_and_teacher.is_active', 'rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.email', 'rto_staff_and_teacher.staff_type', 'rto_staff_and_teacher.city_town', 'rto_staff_and_teacher.id')
            ->get();

        $data = [];
        foreach ($resultArr as $row) {
            $actionHtml = '';
            $actionHtml .= '<li><a href="'.route('edit-staff', ['id' => $row['id']]).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="View/Edit staff profile"> <i class="fa fa-pencil "></i> </a></li>';
            $actionHtml .= '<li><a href="'.route('list-staff-materials', ['folder' => 'StaffFiles', 'id' => $row['id'], 'flag' => ($row['position'] == 7) ? '1' : '0']).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="View/Upload/Download staff document"> <i class="fa fa-file-text "></i></a></li>';
            if ($row['position'] == 7) {
                $actionHtml .= '<li><a href="'.route('teacher-communication-log', ['id' => $row['id'], 'flag' => '1']).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="View/Add staff communication/diary log"><i class="fa fa-user "></i></a></li>';
            } else {
                $actionHtml .= '<li><a href="'.route('staff-communication-log', ['id' => $row['id'], 'flag' => '1']).'" class="link-black text-sm" data-toggle="tooltip" data-original-title="View/Add staff communication/diary log"> <i class="fa fa-user "></i> </a></li>';
            }
            $actionHtml .= '<li><a data-id="'.$row['id'].'" class="link-black text-sm activeDeactiveCheck" data-toggle="tooltip"
                                               data-original-title="'.($row['is_active'] == 1 ? 'Deactivate the selected staff?' : 'Active the selected staff?').'">
                                                <i class="'.($row['is_active'] == 1 ? 'fa fa-lock' : 'fa fa-unlock-alt').'"></i> </a> </li>';
            $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['id'].'" data-target="#deleteModal"> <a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i> </a> </span></li>';

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                '.$actionHtml.'
                            </ul>
                        </div>';
            $status = ($row['is_active'] == 1) ? 'checked="checked"' : '';
            $nestedData = [];
            $nestedData[] = $row['staff_number'].$action;
            $nestedData[] = ($row['position'] == 7) ? 'Staff Teacher' : 'Staff';
            $nestedData[] = $row['first_name'];
            $nestedData[] = $row['username'];
            $nestedData[] = $row['email'];
            $nestedData[] = '<div class=""> <input type="checkbox" class="flat-red label-value-view applicable custom-checkbox-input single" name="status"  id="status" '.$status.' disabled>
                             <label style="" for="status" class="control-label custom-checkbox"> </label></div>';
            $nestedData[] = $row['address'];
            $nestedData[] = $row['city_town'];
            $data[] = $nestedData;
        }

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function staffDelete($staffId)
    {
        $getUserId = Staff::where('college_id', '=', Auth::user()->college_id)->where('id', '=', $staffId)->get(['user_id']);
        $deleteStaff = Staff::where('id', '=', $staffId)->delete();

        return $getUserId;
    }

    public function getStaffRoleList()
    {
        $arrStaff = Staff::pluck('position', 'id')->toArray();
        $staffRecord[''] = '- - Select Staff Role - -';

        return $returnStaffRecord = $staffRecord + $arrStaff;
    }

    public function _getStaffData($staffType)
    {

        $arrStaff = Staff::where('college_id', '=', Auth::user()->college_id);
        if ($staffType == 'Teacher') {
            $arrStaff->where('staff_type', '=', 'Staff-Teacher');
        }

        return $result = $arrStaff->get(['first_name', 'last_name', 'id'])
            ->toArray();
    }

    public function getStaffNameList($collegeId)
    {
        $arrStaff = Staff::where('college_id', '=', $collegeId)->pluck('first_name', 'id')->toArray();
        $staffRecord[''] = '- - Select Staff - -';

        return $returnStaffRecord = $staffRecord + $arrStaff;
    }

    public function getStaffNameListData($collegeId)
    {
        return $arrStaff = Staff::where('college_id', '=', $collegeId)->pluck('first_name', 'id')->toArray();
    }

    public function getFilterStaffList($college_id, $perPage, $filter_by, $status, $search_string)
    {

        $arrStaffList = Staff::leftjoin('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            // ->where('rto_staff_and_teacher.college_id', '=', Auth::user()->college_id)
            ->where('rto_staff_and_teacher.college_id', '=', $college_id)
            ->orderBy('rto_staff_and_teacher.id', 'DESC')
            ->select('rto_users.username', 'rto_staff_and_teacher.*');

        if ($filter_by == 'status' && $status != '') {
            $arrStaffList->where('rto_staff_and_teacher.is_active', $status);
        }

        if ($search_string != '' && $filter_by == 'uname') {
            $arrStaffList->where('rto_users.username', 'like', '%'.$search_string.'%');
        }

        if ($search_string != '' && $filter_by == 'fname') {
            $arrStaffList->where('rto_staff_and_teacher.first_name', 'like', '%'.$search_string.'%');
        }

        if ($search_string != '' && $filter_by == 'lname') {
            $arrStaffList->where('rto_staff_and_teacher.last_name', 'like', '%'.$search_string.'%');
        }

        if ($search_string != '' && $filter_by == 'email') {
            $arrStaffList->where('rto_staff_and_teacher.email', 'like', '%'.$search_string.'%');
        }

        $resultData = $arrStaffList->get();

        // $resultData = $arrStaffList->paginate($perPage);
        return $resultData;
    }

    public function activeDeactiveStaff($id)
    {

        $arrStaff = Staff::select('is_active')
            ->where('id', $id)
            ->get()
            ->toArray();

        $is_active = $arrStaff[0]['is_active'];

        if ($is_active == 1) {
            $tid = 0;
        } elseif ($is_active == 0) {
            $tid = 1;
        }
        $objStaff = Staff::find($id);
        $objStaff->is_active = $tid;
        $objStaff->save();
    }

    public function getStaffList($collegeId)
    {

        // rto_user_role_type
        $arrListSelect = [];
        $arrList = [];
        $arrUsers = Staff::from('rto_staff_and_teacher as rst')
            ->leftjoin('rto_user_role_type as rurt', 'rurt.staff_id', '=', 'rst.id')
            ->where('rst.college_id', $collegeId)
            ->where('rurt.role_type', '9')      // 9 for SAdmin
            ->get(['rst.id', 'rst.name_title', 'rst.first_name', 'rst.last_name'])
            ->toArray();

        for ($i = 0; $i < count($arrUsers); $i++) {
            $arrList[$arrUsers[$i]['id']] = $arrUsers[$i]['name_title'].' '.$arrUsers[$i]['first_name'].' '.$arrUsers[$i]['last_name'];
        }

        $arrListSelect['0'] = '- - Select Account Manager - -';
        $arrAccount = $arrListSelect + $arrList;

        return $arrAccount;
    }

    public function getTeacherList($collegeId)
    {
        $arrTeacherList = Staff::where('college_id', $collegeId)
            ->where('staff_type', 'Staff-Teacher')
            ->where('is_active', '1')
            ->get(['id', 'name_title', 'first_name', 'last_name']);

        $result = [];
        if ($arrTeacherList->count() > 0) {
            foreach ($arrTeacherList as $row) {
                $result[$row->id] = $row->name_title.' '.$row->first_name.' '.$row->last_name;
            }
        } else {
            $result[''] = '- - No Record Found - -';
        }

        return $result;
    }

    public function getStaffListByRole($collegeId, $staffRole)
    {
        $arrStaff = Staff::where('college_id', $collegeId)
            ->where('position', $staffRole)
            ->pluck('first_name', 'id')
            ->toArray();

        $result = [];
        if (count($arrStaff) > 0) {
            $result = $arrStaff;
        } else {
            $result[''] = 'No Staff Found';
        }

        return $result;
    }

    public function getAssignStaffList($collegeId)
    {

        return Staff::join('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->where('rto_staff_and_teacher.college_id', '=', $collegeId)
            ->orderBy('rto_staff_and_teacher.id', 'DESC')
            ->get(['rto_users.username', 'rto_staff_and_teacher.*']);
    }

    public function getSupervisorList($collegeId)
    {
        // 8 for Supervisor
        $arrSupervisorList = Staff::where('college_id', $collegeId)
            // ->where('position', '8')
            ->get(['id', 'name_title', 'first_name', 'last_name']);

        $result = [];
        if ($arrSupervisorList->count() > 0) {
            foreach ($arrSupervisorList as $row) {
                $result[$row->id] = $row->name_title.' '.$row->first_name.' '.$row->last_name;
            }
        } else {
            $result[''] = '- - No Supervisor Found - -';
        }

        return $result;
    }

    public function SetStaffUserPermission($perPage, $rollId, $name)
    {

        $sql = Staff::join('rto_users', 'rto_users.id', '=', 'rto_staff_and_teacher.user_id')
            ->leftjoin('rto_user_role_type', 'rto_user_role_type.staff_id', '=', 'rto_staff_and_teacher.id')
            ->where('rto_user_role_type.role_type', '=', $rollId)
            ->where('rto_staff_and_teacher.college_id', '=', Auth::user()->college_id)
            ->orderBy('rto_staff_and_teacher.id', 'DESC');
        if ($name != '' && ! empty($name)) {
            $sql->where('rto_staff_and_teacher.first_name', 'like', '%'.$name.'%');
            $sql->orWhere('rto_staff_and_teacher.last_name', 'like', '%'.$name.'%');
            $sql->orWhere('rto_staff_and_teacher.name_title', 'like', '%'.$name.'%');
        }
        $arrStaff = $sql->select('rto_users.username', 'rto_staff_and_teacher.name_title', 'rto_staff_and_teacher.first_name', 'rto_staff_and_teacher.last_name', 'rto_staff_and_teacher.user_id')
            ->paginate($perPage);

        return $arrStaff;
    }

    public function getTeacherAndStaffCountByCountryId($countryId, $collegeId)
    {

        return Staff::where('college_id', '=', $collegeId)
            ->where('country', '=', $countryId)->count();
    }

    public function getStaffId($userId, $collegeId)
    {
        return Staff::where('college_id', $collegeId)
            ->where('user_id', $userId)
            ->select('id', DB::raw('concat(name_title," ",first_name," ",last_name) as fullName'))
            ->get();
    }

    public function getCourseByStaff($userId, $collegeId)
    {

        $arrStaff = Staff::leftjoin('rto_student_training_plan', 'rto_student_training_plan.trainer', '=', 'rto_staff_and_teacher.id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_training_plan.course_id')
            ->where('rto_staff_and_teacher.college_id', '=', $collegeId)
            ->where('rto_staff_and_teacher.user_id', '=', $userId)
            ->groupby('rto_courses.id')
            ->get([
                'rto_courses.id',
                'rto_courses.course_name',
                'rto_courses.course_code',
            ])
            ->toarray();
        $courseArray = [];
        for ($i = 0; $i < count($arrStaff); $i++) {
            if (isset($arrStaff[$i]['course_code']) && isset($arrStaff[$i]['course_name'])) {
                $courseArray[$arrStaff[$i]['id']] = $arrStaff[$i]['course_code'].':'.$arrStaff[$i]['course_name'];
            }
        }
        if (count($courseArray) == 0) {
            $courseArray[''] = 'No Course Found';
        }

        return $courseArray;
    }

    public function getStaffCoursesEmailContent($teacherId)
    {
        $sql = Staff::from('rto_staff_and_teacher as rst')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'rst.country')
            ->leftjoin('rto_colleges as clg', 'rst.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->where('rst.id', '=', $teacherId);

        return $result = $sql->select(
            DB::raw('concat(rst.name_title, rst.first_name, " ", rst.last_name) as teacher_name'),
            'country1.name as country_name',
            'clg.contact_email as college_email',
            'clg.college_name as entity_name',
            'rst.*'
        )
            ->get()->toarray();
    }

    public function getStaffs($postArray)
    {
        $perpage = $postArray['perpage'];
        $currentpage = $postArray['page'];

        $query = StaffPosition::select('id', 'position', 'updated_at')->groupBy('position')->orderBy('created_at', 'DESC');
        if ($perpage != '' && $currentpage != '') {
            $query->offset(($currentpage - 1) * $perpage);
            $query->limit($perpage);
        }
        $data = $query->get()->toArray();

        for ($i = 0; $i < count($data); $i++) {
            $data[$i]['staff_count'] = Staff::where('position', $data[$i]['id'])->count();
        }

        return $data;
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function scopeFilterQuery(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('first_name', 'like', "%$value%")
            ->orWhere('last_name', 'like', "%$value%");
    }

    public function scopeFilterPosition(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('position', $value);
    }
}
