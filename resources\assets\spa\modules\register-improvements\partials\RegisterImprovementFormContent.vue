<template>
    <form-element>
        <fieldset class="h-screen-header flex flex-col">
            <div class="grid flex-1 grid-cols-2 gap-6 overflow-y-auto px-6 py-4">
                <field
                    :id="'user_type'"
                    :name="'user_type'"
                    :label="'User Type:'"
                    :component="'dropdownTemplate'"
                    :validator="requiredtrue"
                    :data-items="store.userTypes ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select User Type',
                        value: null,
                    }"
                    v-model="store.formData['user_type']"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'staff_role'"
                    :name="'staff_role'"
                    :label="'Staff Role'"
                    :component="'dropdownTemplate'"
                    :validator="requiredtrue"
                    :data-items="staffRoles ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Staff Role',
                        value: null,
                    }"
                    v-model="store.formData['staff_role']"
                    @change="handleStaffRoleChange"
                    v-if="store.formData['user_type'] === '1'"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'requested_by'"
                    :name="'requested_by'"
                    :label="'Requested By'"
                    :component="'dropdownTemplate'"
                    :validator="requiredtrue"
                    :data-items="staffNameOptions ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Requested By',
                        value: null,
                    }"
                    v-model="store.formData['requested_by']"
                    v-if="store.formData['user_type'] === '1'"
                    :pt="{ root: 'col-span-2' }"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'category'"
                    :name="'category'"
                    :label="'Category'"
                    :component="'dropdownTemplate'"
                    :validator="requiredtrue"
                    :data-items="categories ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Category',
                        value: null,
                    }"
                    v-model="store.formData['category']"
                    :pt="{
                        root: store.formData['category'] === 'new' ? 'col-span-1' : 'col-span-2',
                    }"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'category_add'"
                    :name="'category_add'"
                    :label="'New Category'"
                    :component="'inputTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['category_add']"
                    v-if="store.formData['category'] === 'new'"
                >
                    <template #inputTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'case_detail'"
                    :name="'case_detail'"
                    :label="'Case Detail'"
                    :component="'textAreaTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['case_detail']"
                    :pt="{ root: 'col-span-2' }"
                >
                    <template #textAreaTemplate="{ props }">
                        <FormTextArea
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'escalated_to'"
                    :name="'escalated_to'"
                    :label="'Escalated To'"
                    :component="'dropdownTemplate'"
                    :validator="requiredtrue"
                    :data-items="staffNameOptions ?? []"
                    :text-field="'text'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    :default-item="{
                        text: 'Select Escalated To',
                        value: null,
                    }"
                    v-model="store.formData['escalated_to']"
                    :pt="{ root: 'col-span-2' }"
                >
                    <template #dropdownTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'action_taken'"
                    :name="'action_taken'"
                    :label="'Action Taken'"
                    :component="'textAreaTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['action_taken']"
                    :pt="{ root: 'col-span-2' }"
                >
                    <template #textAreaTemplate="{ props }">
                        <FormTextArea
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'action_taken_prevent'"
                    :name="'action_taken_prevent'"
                    :label="'Action Taken To Prevent Reoccurence'"
                    :component="'textAreaTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['action_taken_prevent']"
                    :pt="{ root: 'col-span-2' }"
                >
                    <template #textAreaTemplate="{ props }">
                        <FormTextArea
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'logged_by'"
                    :name="'logged_by'"
                    :label="'Logged By'"
                    :component="'inputTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['logged_by']"
                    :disabled="true"
                >
                    <template #inputTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <field
                    :id="'logged_date'"
                    :name="'logged_date'"
                    :label="'Logged Date'"
                    :component="'dateTemplate'"
                    :validator="requiredtrue"
                    v-model="store.formData['logged_date']"
                >
                    <template #dateTemplate="{ props }">
                        <FormDatePicker
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="flex items-center justify-end gap-4 border-t px-6 py-3">
                <Button size="base" type="submit" variant="primary" class="min-w-[100px]">
                    <span>Save</span>
                </Button>
                <Button size="base" variant="secondary" @click="store.formDialog = false">
                    <span>Cancel</span>
                </Button>
            </div>
        </fieldset>
    </form-element>
</template>
<script setup>
import { inject, ref, computed } from 'vue';
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import { useStaffPositionStore } from '@spa/stores/modules/staff/useStaffPositionStore.js';
import { useStaffStore } from '@spa/stores/modules/staff/useStaffStore.js';
import Button from '@spa/components/Buttons/Button.vue';

const kendoForm = inject('kendoForm', {});

const store = useRegisterImprovementStore();
const staffStore = useStaffStore();
const staffPositionStore = useStaffPositionStore();

const staffNameOptions = computed(() => {
    return staffStore.all.map((item) => ({
        text: item.full_name,
        value: item.id,
    }));
});

const staffRoles = computed(() => {
    return staffPositionStore.all.map((item) => ({
        text: item.position,
        value: item.id,
    }));
});

const categories = computed(() => {
    return [{ text: '+ Add New Category', value: 'new' }, ...store.categories];
});

const handleStaffRoleChange = (e) => {
    const value = e.value;
    kendoForm.onChange('staff_role', {
        value: value,
    });
    staffStore.filters.position = value;
};
</script>
<style lang=""></style>
