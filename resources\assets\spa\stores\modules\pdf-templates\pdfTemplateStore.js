import useConfirm from '@spa/services/useConfirm';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const usePdfTempolateStore = defineStore('usePdfTemplateStore', () => {
    const storeUrl = ref('v2/tenant/pdf-templates');
    const confirm = useConfirm();

    const commonStoreProps = useCommonStore(storeUrl.value);

    const { loading, formData, contextLoading } = commonStoreProps;

    const updateDefaultTemplate = async (payload) => {
        confirm.require({
            message: 'Are you sure you want to set this template as the default?',
            header: 'Set as Default PDF Template?',
            icon: 'pi pi-exclamation-triangle',
            variant: 'info',
            acceptLabel: 'Confirm',
            rejectLabel: 'Cancel',
            width: 500,
            accept: async () => {
                contextLoading('update-default', true);
                try {
                    return await $http.post(
                        `/api/${storeUrl.value}/${formData.value.id}/update-default`,
                        payload
                    );
                } catch (e) {
                    console.error('Error updating template:', e);
                    return { error: e.message || 'Failed to update template' };
                } finally {
                    contextLoading('update-default', false);
                }
            },
            reject: () => {
                return false;
            },
            onHide: () => {
                return false;
            },
        });
    };
    return {
        ...commonStoreProps,
        updateDefaultTemplate,
    };
});
