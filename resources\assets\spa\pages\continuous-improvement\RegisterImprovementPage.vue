<script setup>
import RegisterImprovementsListComponent from '@spa/modules/register-improvements/RegisterImprovementsListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Register Improvement" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Register Improvement" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <RegisterImprovementsListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
