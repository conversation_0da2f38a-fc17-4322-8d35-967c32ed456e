<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class RegisterImprovementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $requester = null;
        if ($this->user_type === 1 && $this->relationLoaded('staffUser') && $this->staffUser) {
            $requester = [
                'id' => $this->staffUser->id,
                'full_name' => $this->staffUser->name_title.' '.$this->staffUser->first_name.' '.$this->staffUser->last_name,
                'role' => $this->staffUser->position,
            ];
        } elseif ($this->user_type === 2 && $this->relationLoaded('studentUser') && $this->studentUser) {
            $requester = [
                'id' => $this->studentUser->id,
                'full_name' => $this->studentUser->name_title.' '.$this->studentUser->first_name.' '.$this->studentUser->last_name,
                'role' => 'Student',
            ];
        }

        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'user_type' => $this->user_type,
            'staff_role' => $this->staff_role,
            'requester' => $requester,
            'requested_by' => $this->requested_by,
            'logged_by' => $this->logged_by,
            'logged_by_name' => Auth::user()->name,
            'category' => $this->category,
            'case_detail' => $this->case_detail,
            'escalated_to' => $this->escalated_to,
            'action_taken' => $this->action_taken,
            'action_taken_prevent' => $this->action_taken_prevent,
            'status' => $this->status,
            'logged_date' => $this->lodged_date,
            'updated_at' => $this->updated_at ?? null,
            'created_at' => $this->created_at,
        ];
    }
}
