<?php

namespace GalaxyAPI\Requests;

use GalaxyAPI\Traits\HasRequestHelpers;
use Illuminate\Foundation\Http\FormRequest;

class RegisterImprovementRequest extends FormRequest
{
    use HasRequestHelpers;

    public function rules(): array
    {
        $rules = $this->filterRulesForPresentFields([
            'id' => 'required|integer|exists:your_table,id',
            'college_id' => 'required|integer',
            'user_type' => 'required|integer',
            'staff_role' => 'nullable|string|max:100',
            'requested_by' => 'required|integer',
            'logged_by' => 'required|integer',
            'category' => 'required|string|max:255',
            'case_detail' => 'required|string',
            'escalated_to' => 'nullable|integer',
            'action_taken' => 'nullable|string',
            'action_taken_prevent' => 'nullable|string',
            'status' => 'required|string|in:open,in_progress,resolved,closed', // adjust as needed
            'lodged_date' => 'required|date',
            'created_at' => 'nullable|date',
            'updated_at' => 'nullable|date',
        ]);

        return $rules;
    }
}
