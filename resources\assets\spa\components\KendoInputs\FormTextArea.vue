<template>
    <fieldwrapper :class="rootClasses">
        <klabel :class="labelClasses" :editor-id="id" :editor-valid="valid">
            {{ label }}
            <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
        </klabel>
        <div class="k-form-field-wrap w-full">
            <span class="k-textarea">
                <ktextarea
                    :valid="valid"
                    :value="value"
                    :id="id"
                    :maxlength="max"
                    :rows="rows"
                    @input="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                    :class="fieldClasses"
                    :disabled="disabled"
                    :placeholder="placeholder"
                ></ktextarea>
            </span>
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <div v-else style="display: flex; justify-content: space-between">
                <hint>{{ hint }}</hint>
            </div>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { TextArea } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: {
            type: Array,
            default: () => [],
        },
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        max: Number,
        valid: Boolean,
        value: {
            type: String,
            default: '',
        },
        rows: Number,
        pt: {
            type: Object,
            default: {},
        },
        disabled: Boolean,
        indicaterequired: { type: Boolean, default: false },
        placeholder: { type: String, default: '' },
    },
    components: {
        ktextarea: TextArea,
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        labelClasses() {
            return twMerge('k-form-label', this.pt.label);
        },
        fieldClasses() {
            return twMerge('tw-input__checkbox', this.className, this.pt.field);
        },
        rootClasses() {
            return twMerge('tw-form__wrapper', this.pt.root);
        },
    },
    methods: {
        handleChange(e) {
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
