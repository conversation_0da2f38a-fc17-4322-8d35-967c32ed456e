<?php

namespace GalaxyAPI\Controllers;

use App\Model\RegisterImprovment;
use Auth;
use GalaxyAPI\Requests\RegisterImprovementRequest;
use GalaxyAPI\Resources\RegisterImprovementResource;
use Illuminate\Support\Facades\Config;

class RegisterImprovementController extends CrudBaseController
{
    public function __construct()
    {
        // $this->scopeWithValue = [
        //     'collegeId' => Auth::user()->college_id,
        // ];
        $this->withAll = [
            'staffUser',
            'studentUser',
        ];
        $this->loadAll = [
            'staffUser',
            'studentUser',
        ];
        parent::__construct(
            model: RegisterImprovment::class,
            storeRequest: RegisterImprovementRequest::class,
            updateRequest: RegisterImprovementRequest::class,
            resource: RegisterImprovementResource::class,
        );
    }

    public function formConstants()
    {
        $userTypes = Config::get('constants.arrUserType');
        $category = RegisterImprovment::getCategoryList();

        return ajaxSuccess([
            'user_types' => $userTypes,
            'catagory' => $category,
        ], '');
    }
}
