<template>
    <fieldwrapper :class="rootClasses">
        <klabel
            :id="labelId"
            :editor-id="id"
            :editor-valid="valid"
            :editor-disabled="disabled"
            :class="labelClasses"
        >
            {{ label }}
            <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
        </klabel>
        <radiogroup
            :aria-describedby="describedBy"
            :label="label"
            :valid="valid"
            :value="value"
            :layout="layout"
            :data-items="dataItems"
            :id="id"
            :checked="value"
            @change="handleChange"
            @blur="handleBlur"
            @focus="handleFocus"
            :class="fieldClasses"
            :disabled="disabled"
        />
        <error v-if="showValidationMessage">
            {{ validationMessage }}
        </error>
        <hint>{{ hint }}</hint>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { RadioGroup } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: [String, Object, Number, Boolean],
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        valid: Boolean,
        disabled: Boolean,
        layout: String,
        dataItems: Array,
        value: {
            type: [Boolean, Number, String],
        },
        className: String,
        pt: {
            type: Object,
            default: {},
        },
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        radiogroup: RadioGroup,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        describedBy() {
            return `${this.hintId} ${this.errorId}`;
        },
        labelId() {
            return this.label ? `${this.id}_label` : '';
        },
        labelClasses() {
            return twMerge('mb-1 font-medium leading-5 text-gray-700', this.pt.label);
        },
        fieldClasses() {
            return twMerge('tw-input__radio-group', this.className, this.pt.field);
        },
        rootClasses() {
            if (this.orientation == 'horizontal') {
                return twMerge('tw-form__fieldwrapper field-horizontal', this.pt.root);
            }
            return twMerge('tw-form__fieldwrapper', this.pt.root);
        },
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    methods: {
        handleChange(e) {
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
