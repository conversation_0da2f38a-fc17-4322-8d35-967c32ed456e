$(document).ready(function () {
    var tempECT = 0;
    var tempECE = 0;
    var ecFormId = '#enrollNewCourseForm';
    var ecModalId = '#enrollNewCourseModal';
    var eceFormId = '#enrollEditCourseForm';
    var eceModalId = '#enrollEditCourseModal';
    var addFeeScheduleDialogId = '#addFeeScheduleDialog';
    var enrollModalFlag = true;
    var editEnrollModalFlag = true;

    $(addFeeScheduleDialogId).kendoDialog({
        width: '600px',
        title: 'Create Offer Fee Schedule?',
        content:
            "Would you like to create offer fee schedule for this student now? You can do this later from More Actions>Generate Offer Fee Schedule also. <input type='hidden' name='id' id='selectedCourseId' />",
        actions: [
            { text: 'Do Later' },
            {
                text: 'Yes, Create Fee Schedule',
                primary: true,
                action: function () {
                    let selectedCourseId = $(addFeeScheduleDialogId)
                        .find('#selectedCourseId')
                        .val();
                    openFeeScheduleModal(selectedCourseId);
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(addFeeScheduleDialogId),
        visible: false,
    });

    toggleFormDisableAttr(ecFormId, '#createEnrollNewCourseBtn');

    handleScrollTabChange(
        '.enrollNewCourseModal-wrap',
        '.enroll-course-section',
        '.enroll-course-bar'
    );

    handleTabClickScroll(
        '.enrollCourseTab',
        '.enrollNewCourseModal-wrap',
        '.enroll-course-bar',
        85
    );

    handleScrollTabChange(
        '.enrollEditCourseModal-wrap',
        '.edit-enroll-course-section',
        '.edit-enroll-course-bar'
    );

    handleTabClickScroll(
        '.editEnrollCourseTab',
        '.enrollEditCourseModal-wrap',
        '.edit-enroll-course-bar',
        85
    );

    $(ecModalId).kendoWindow(defaultWindowSlideFormat('Enroll New Course', 65));
    $(eceModalId).kendoWindow(defaultWindowSlideFormat('Edit Enroll Course', 65));

    $('body').on('click', '.enrollNewCourseBtn', function () {
        // TODO: TEMPLATE:RENDER
        // TemplateLoader.loadTemplatesByCategory("enroll");
        kendoWindowOpen(ecModalId);
        $(ecModalId).parent().addClass('student-custom-modal-wrapper');
        ajaxActionV2(
            'api/get-student-details',
            'POST',
            selectedDataArr,
            function (response) {
                setAddEnrollFormInputs(response.data);
            },
            false
            // function (flag) {
            //     console.log("enrollModalFlag", enrollModalFlag);
            //     if (enrollModalFlag) {
            //         toggleContentLoader("#enrollNewCourseModal", flag);
            //     }
            // },
        );
    });

    $('body').on('click', '.editEnrollCourseBtn', function () {
        // TODO: TEMPLATE:RENDER
        // TemplateLoader.loadTemplatesByCategory("editEnroll");
        kendoWindowOpen(eceModalId);
        ajaxActionV2(
            'api/get-student-course-details',
            'POST',
            selectedDataArr,
            function (response) {
                setEditEnrollFormInputs(response.data);
                // setTimeout(() => {
                //     editEnrollModalFlag = false;
                // });
            },
            false
            // function (flag) {
            //     if (editEnrollModalFlag) {
            //         toggleContentLoader("#enrollEditCourseModal", flag);
            //     }
            // },
        );
    });

    $('body').on('change', '#campus_id_add', function (e) {
        e.preventDefault();
        let campusId = $(this).val();
        // getDropdown("course_id_add","", 'get-campus-course-list',{'campus_id': campusId},'Select Course');

        function valueTemplate(dataItem) {
            let hasSupersededDate = dataItem.is_superseded
                ? "<div class='flex items-center justify-center px-2.5 py-0.5 bg-red-100 rounded-md'><p class='text-sm leading-4 text-center text-red-500 truncate'>Superseded</p></div>"
                : '';
            return `<div data-is_superseded="${dataItem.is_superseded}"  data-superseded_date_expired="${dataItem.superseded_date_expired}" data-superseded_date="${dataItem.superseded_date}" class="inline-flex space-x-1 items-end justify-between getSupersededDate">${hasSupersededDate}
                <p class="text-sm leading-4">${dataItem.Name}</p></div>`;
        }

        $('#course_id_add').kendoDropDownList({
            filter: 'contains',
            filterInput: {
                width: '100%',
            },
            optionLabel: 'Select Course',
            optionLabelTemplate: '<span> Select Course</span>',
            valueTemplate: valueTemplate,
            template: valueTemplate,
            dataTextField: 'Name',
            dataValueField: 'Id',
            dataType: 'json',
            select: function (e) {
                let courseId = e.dataItem.Id;
                getCollageEnrolmentFees(courseId);
                // getCourseCampus(courseId);
                getResultCalculationMethod(courseId);
                getCourseTemplateList(courseId);

                let is_superseded = e.dataItem.is_superseded;
                let superseded_date = e.dataItem.superseded_date;
                let superseded_date_expired = e.dataItem.superseded_date_expired;
                let superseded_date_expired_no_format =
                    e.dataItem.superseded_date_expired_no_format;
                $('#courseIsSupersededModel').find('.superseded_date').text(superseded_date);
                $('#courseIsSupersededModel')
                    .find('.superseded_date_expire')
                    .text(superseded_date_expired);
                var TodayDate = new Date();
                var endDate = new Date(Date.parse(superseded_date_expired_no_format));
                if (is_superseded == 1) {
                    if (TodayDate > endDate) {
                        $('#courseIsSupersededExpiredModel').data('kendoDialog').open();
                    } else if (superseded_date) {
                        $('#courseIsSupersededModel').data('kendoDialog').open();
                    }
                }
            },
            dataSource: {
                schema: { data: 'data' },
                transport: getTransportReadOnly('get-campus-course-list', {
                    campus_id: campusId,
                }),
            },
            value: '',
        });
    });

    // $("body").on("change", '#course_id_add', function(e){
    //     e.preventDefault();
    //     let courseId = $(this).val();
    //     getCollageEnrolmentFees(courseId);
    //     // getCourseCampus(courseId);
    //     getResultCalculationMethod(courseId);
    //     getCourseTemplateList(courseId);

    //     let superseded_date = $(this).parent('span').find('.getSupersededDate').attr('data-superseded_date');
    //     let superseded_date_expired = $(this).parent('span').find('.getSupersededDate').attr('data-superseded_date_expired');

    //     $("#courseIsSupersededModel").find('.superseded_date').text(superseded_date);
    //     $("#courseIsSupersededModel").find('.superseded_date_expire').text(superseded_date_expired);
    //     var TodayDate = new Date();
    //     var endDate = new Date(Date.parse(superseded_date_expired));

    //     if (endDate < TodayDate) {
    //         $("#courseIsSupersededExpiredModel").data("kendoDialog").open();
    //     }else if(superseded_date){
    //         $("#courseIsSupersededModel").data("kendoDialog").open();
    //     }
    // });
    $('body').on('change', '#intake_year', function (e) {
        e.preventDefault();
        let courseId = $('#course_id_add').val();
        let intakeYear = $('#intake_year').val();
        getCollageEnrolmentFees(courseId, intakeYear);
    });
    $('body').on('change', '#edit_intake_year', function (e) {
        e.preventDefault();
        let courseId = $('#course_id_edit').val();
        let intakeYear = $('#edit_intake_year').val();
        getCollageEnrolmentFeesEdit(courseId, intakeYear);
    });

    $('body').on('change', '#intake_date', function (e) {
        e.preventDefault();
        let courseId = $('#course_id_add').val();
        let intakeYear = $('#intake_year').val();
        let startDate = $('#intake_date').val();
        appendStartDate();
        getFinishDate(true);
        // getCollageEnrolmentFees(courseId, intakeYear, startDate);
    });
    $('body').on('change', '#edit_intake_date', function (e) {
        e.preventDefault();
        let courseId = $('#course_id_edit').val();
        let intakeYear = $('#edit_intake_year').val();
        let startDate = $('#edit_intake_date').val();
        appendStartDateEdit();
        getFinishDate(false);
        // getCollageEnrolmentFeesEdit(courseId, intakeYear, startDate);
    });

    $('body').on('keyup', '#total_weeks', function (e) {
        e.preventDefault();
        getFinishDate(true);
    });
    $('body').on('keyup', '#edit_total_weeks', function (e) {
        e.preventDefault();
        getFinishDate(false);
    });
    $('body').on('change', '#ec_start_date', function (e) {
        e.preventDefault();
        getFinishDate(true);
    });
    $('body').on('change', '#edit_start_date', function (e) {
        e.preventDefault();
        getFinishDate(false);
    });
    $('body').on('change', '#ec_finish_date', function (e) {
        e.preventDefault();
        getFinishDate(true);
    });
    $('body').on('change', '#ec_finish_date', function (e) {
        e.preventDefault();
        let startDate = new Date($('#ec_start_date').val());
        let intakeDate = startDate;
        let finishDate = new Date($(this).val());
        if (startDate != '' && finishDate != '') {
            if (Date.parse(intakeDate) > Date.parse(finishDate)) {
                $('#ec_finish_date').val('');
                notificationDisplay('Finish Date must be Greater from Intake Date', '', 'error');
                //return;
            } else if (Date.parse(startDate) > Date.parse(finishDate)) {
                $('#ec_finish_date').val('');
                notificationDisplay('Finish Date must be Greater from Start Date', '', 'error');
                //return;
            }

            // Calculate total duration from start date to finish date
            calculateTotalWeeksFromDates('#ec_start_date', '#ec_finish_date', '#total_weeks');
        }
    });
    $('body').on('change', '#edit_finish_date', function (e) {
        e.preventDefault();
        let startDate = new Date($('#edit_start_date').val());
        let intakeDate = startDate;
        let finishDate = new Date($(this).val());
        if (startDate != '' && finishDate != '') {
            if (Date.parse(intakeDate) > Date.parse(finishDate)) {
                $('#edit_finish_date').val('');
                notificationDisplay('Finish Date must be Greater from Intake Date', '', 'error');
                //return;
            } else if (Date.parse(startDate) > Date.parse(finishDate)) {
                $('#edit_finish_date').val('');
                notificationDisplay('Finish Date must be Greater from Start Date', '', 'error');
                //return;
            }

            // Calculate total duration from start date to finish date
            calculateTotalWeeksFromDates(
                '#edit_start_date',
                '#edit_finish_date',
                '#edit_total_weeks'
            );
        }
    });
    $('body').on('change', '#extend_finish_date', function (e) {
        e.preventDefault();
        let startDate = new Date($('#extend_start_date').val());
        let finishDate = new Date($(this).val());

        if (startDate != '' && finishDate != '') {
            if (Date.parse(startDate) > Date.parse(finishDate)) {
                $('#extend_finish_date').val('');
                notificationDisplay('Finish Date must be Greater from Start Date', '', 'error');
                //return;
            }

            // Calculate total duration from start date to finish date
            calculateTotalWeeksFromDates(
                '#extend_start_date',
                '#extend_finish_date',
                '#extend_total_weeks'
            );
            $(document).find('#extend_total_weeks_display').text($('#extend_total_weeks').val());
        }
    });

    // Event handlers for duration type changes, Recalculate finish date
    $('body').on('change', '#course_duration_type', function (e) {
        e.preventDefault();
        getFinishDate(true);
    });
    $('body').on('change', '#edit_course_duration_type', function (e) {
        e.preventDefault();
        getFinishDate(false);
    });
    $('body').on('click', '.extendCourseDueDateBtn', function (e) {
        e.preventDefault();

        // Get course details for extension
        ajaxActionV2('api/student-course-detail', 'POST', selectedDataArr, function (response) {
            if (response.status == 'error') {
                notificationDisplay(response.message, '', response.status);
                return false;
            }
            let res = response.data[0];
            let extendTemplate = kendo.template($('#studentCourseExtendTemplate').html())({
                arr: res,
            });
            $(document).find('#studentCourseExtendHtml').html(extendTemplate);

            ajaxActionV2(
                'api/get-course-extension-history',
                'POST',
                selectedDataArr,
                function (historyResponse) {
                    let historyTemplate = kendo.template(
                        $('#studentCourseExtensionHistoryTemplate').html()
                    )({
                        arr: historyResponse.data.data || [],
                    });
                    $(document).find('#studentCourseExtensionHistoryHtml').html(historyTemplate);
                }
            );

            // Initialize form elements
            setTimeout(() => {
                setUpCourseExtensionForm(res);
            }, 300);

            kendoWindowOpen('#studentCourseExtendModal');
        });
    });

    // Course Extension Modal Event Handlers
    $('body').on('click', '.showHideExtensionHistory', function (e) {
        e.preventDefault();
        $('.extensionHistoryDiv').toggleClass('hidden');
        if ($('.extensionHistoryDiv').hasClass('hidden')) {
            $('.extensionHistoryDivTitle').text('View');
        } else {
            $('.extensionHistoryDivTitle').text('Hide');
        }
    });

    $('body').on('click', '.submitStudentCourseExtend', function (e) {
        e.preventDefault();
        let validator = defaultFormValidator('#studentCourseExtendForm');
        if (!validator.validate()) {
            return false;
        }

        // Get current and new finish dates for comparison
        let currentFinishDate = $('#extend_start_date').data('original-finish-date') || '';
        let newFinishDate = $('#extend_finish_date').val();

        // Check if the finish date is the same as current
        if (
            currentFinishDate &&
            newFinishDate &&
            formatDateForDisplay(currentFinishDate) == newFinishDate
        ) {
            notificationDisplay(
                'The new finish date must be different from the current finish date',
                '',
                'error'
            );
            return false;
        }
        openCourseExtensionConfirmDialog();
    });

    // Event handlers for duration type changes, Recalculate finish date
    $('body').on('change', '#course_duration_type', function (e) {
        e.preventDefault();
        getFinishDate(true);
    });
    $('body').on('change', '#edit_course_duration_type', function (e) {
        e.preventDefault();
        getFinishDate(false);
    });

    $('body').on('click', '.feeValidate', function (e) {
        e.preventDefault();
        var actualFee = $('#course_fee').val();
        var upfrontFee = $('#course_upfront_fee').val();
        if (parseInt(upfrontFee) > parseInt(actualFee)) {
            notificationDisplay('Upfront fee Can not be more than actual fee', '', 'error');
            $('#course_upfront_fee').val('');
        }
    });
    $('body').on('click', '.feeValidateEdit', function (e) {
        e.preventDefault();
        var actualFee = $('#edit_course_fee').val();
        var upfrontFee = $('#edit_course_upfront_fee').val();
        if (parseInt(upfrontFee) > parseInt(actualFee)) {
            notificationDisplay('Upfront fee Can not be more than actual fee', '', 'error');
            $('#edit_course_upfront_fee').val('');
        }
    });

    $('body').on('click', '#createEnrollNewCourseBtn', function (e) {
        e.preventDefault();
        let validator = $(ecFormId)
            .kendoValidator({
                messages: {
                    required: function (input) {
                        return input.data('message');
                    },
                },
            })
            .data('kendoValidator');

        if (!validator.validate()) {
            return false;
        }

        let tempArr = {
            college_id: collegeId,
            student_id: studentId,
            student_course_id: selectedStudCourseID,
        };
        $(document)
            .find(ecFormId)
            .find('input:disabled, select:disabled, textarea:disabled')
            .prop('disabled', false);
        let dataArr = getSerializeFormArray(ecFormId, tempArr);
        $(document)
            .find(ecFormId)
            .find('input:disabled, select:disabled, textarea:disabled')
            .prop('disabled', true);
        if (dataArr) {
            ajaxActionV2('api/save-enroll-student-course', 'POST', dataArr, function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    closeKendoWindow(ecModalId);
                    let lastInsertStudCourseId = parseInt(response.data.id);
                    setTimeout(() => {
                        onSuccessOfCourseEnroll(lastInsertStudCourseId);
                    }, 2000);
                }
            });
        }
    });

    function onSuccessOfCourseEnroll(lastInsertStudCourseId) {
        if (lastInsertStudCourseId > 0) {
            $(addFeeScheduleDialogId).data('kendoDialog').open();
            $(addFeeScheduleDialogId).find('#selectedCourseId').val(lastInsertStudCourseId);

            $(document)
                .find(".header_course_list[data-role='dropdownlist']")
                .each(function () {
                    $(this).data('kendoDropDownList').value(selectedStudCourseID);
                });
        }
    }

    $('body').on('change', '#add_contract_schedule_id', function () {
        ajaxActionV2(
            'api/get-contract_schedule_data',
            'POST',
            {
                contract_schedule_id: $('#add_contract_schedule_id')
                    .data('kendoDropDownList')
                    .value(),
            },
            function (response) {
                $('#defaultContractScheduleSetTab')
                    .find('#purchasing_contract_ode')
                    .val(response.data[0].contract_code);
                $('#defaultContractScheduleSetTab')
                    .find('#schedule_code')
                    .val(response.data[0].schedual_code);
            }
        );
    });
    $('body').on('change', '#edit_contract_schedule_id', function () {
        ajaxActionV2(
            'api/get-contract_schedule_data',
            'POST',
            {
                contract_schedule_id: $('#edit_contract_schedule_id')
                    .data('kendoDropDownList')
                    .value(),
            },
            function (response) {
                $('#editDefaultContractScheduleSetTab')
                    .find('#edit_pcc')
                    .val(response.data[0].contract_code);
                $('#editDefaultContractScheduleSetTab')
                    .find('#edit_schedule_code')
                    .val(response.data[0].schedual_code);
            }
        );
    });
    $('body').on('click', '#updateEnrollCourseBtn', function (e) {
        e.preventDefault();
        updateEnrollCourseDetails();
    });

    $('body').on('click', '#updateAndCloseEnrollCourseBtn', function (e) {
        e.preventDefault();
        updateEnrollCourseDetails(true);
    });

    function updateEnrollCourseDetails(isCloseModal = false) {
        let validator = $(eceFormId)
            .kendoValidator({
                messages: {
                    required: function (input) {
                        return input.data('message');
                    },
                },
            })
            .data('kendoValidator');
        if (!validator.validate()) {
            return false;
        }
        let tempArr = {
            college_id: collegeId,
            student_id: studentId,
            student_course_id: selectedStudCourseID,
        };
        $(document)
            .find(eceFormId)
            .find('input:disabled, select:disabled, textarea:disabled')
            .prop('disabled', false);
        let dataArr = getSerializeFormArray(eceFormId, tempArr);
        $(document)
            .find(eceFormId)
            .find('input:disabled, select:disabled, textarea:disabled')
            .prop('disabled', true);

        if (dataArr) {
            ajaxActionV2('api/update-enroll-student-course', 'POST', dataArr, function (response) {
                notificationDisplay(response.message, '', response.status);
                if (response.status == 'success') {
                    if (isCloseModal) {
                        closeKendoWindow(eceModalId);
                    }
                    updateStudentCourseTabData();
                }
            });
        }
    }

    function updateStudentCourseTabData() {
        setTimeout(function () {
            ajaxActionV2(
                'api/get-student-summary-tab',
                'POST',
                selectedDataArr,
                function (response) {
                    let cDetail = response.data.course_detail;
                    $(document)
                        .find('#studCourseTab')
                        .html(
                            kendo.template($('#studCourseTemplate').html())({
                                arr: cDetail,
                            })
                        );
                    $(document).find('#courseCampus').text(cDetail.campus_name);
                }
            );
        }, 500);
    }

    function getDropdownWithoutFilter(id, value, apiURL, postArr = [], placeholder = '') {
        $('#' + id).kendoDropDownList({
            optionLabel: placeholder,
            dataTextField: 'Name',
            dataValueField: 'Id',
            dataType: 'json',
            dataSource: getDropdownDataSource(apiURL, postArr),
            value: value,
        });
    }
    function getDropdownFromApi(id, value, data, placeholder = '') {
        $('#' + id).kendoDropDownList({
            filter: 'contains',
            optionLabel: placeholder,
            filterInput: {
                width: '100%',
            },
            dataTextField: 'Name',
            dataValueField: 'Id',
            dataType: 'json',
            dataSource: {
                schema: { data: 'data' },
                data: data,
            },
            value: value,
        });
    }

    function setAddEnrollFormInputs(arr) {
        let responseArr = { arr: arr.student_detail };

        let ectList = [
            'generalDetails',
            'intakeDetails',
            'feesDetails',
            'defaultContractScheduleSet',
            'classType',
        ];
        $.each(ectList, function (index, tabName) {
            let tempHtml = kendo.template($(`#${tabName}Template`).html())(responseArr);
            $(document).find(`#${tabName}Tab`).html(tempHtml);
        });
        $(document).find('.isHigherEdDiv').toggle(arr.student_detail.isHigherEd);
        // kendoWindowOpen(ecModalId);
        //manageKendoSwitch(ecFormId);

        defaultSetSwitchValue('#is_claim', 'N');
        defaultSetSwitchValue('#is_qualification', 'N');
        defaultSetSwitchValue('#is_certificate', 'N');
        $('#is_certificate').data('kendoSwitch').enable(false);

        defaultSetSwitchValue('#mode_of_delivery', 'N');
        // defaultSetSwitchValue("#is_finish_dt", 'N');
        defaultSetSwitchValue('#auto_update_to_sru', 'N');
        if (arr.student_detail.student_type != 'Domestic') {
            defaultSetSwitchValue('#is_internal', 'Y');
            defaultSetSwitchValue('#is_external', 'N');
            defaultSetSwitchValue('#workplace_based_delivery', 'N');
            $('#is_internal').data('kendoSwitch').enable(false);
            $('#is_external').data('kendoSwitch').enable(false);
            $('#workplace_based_delivery').data('kendoSwitch').enable(false);
        } else {
            defaultSetSwitchValue('#is_internal', 'N');
            defaultSetSwitchValue('#is_external', 'N');
            defaultSetSwitchValue('#workplace_based_delivery', 'N');
            $('#is_internal').data('kendoSwitch').enable(true);
            $('#is_external').data('kendoSwitch').enable(true);
            $('#workplace_based_delivery').data('kendoSwitch').enable(true);
        }
        // if(arr.student_detail.student_type != 'Domestic'){
        //     defaultSetSwitchValue("#is_internal", 'Y');
        //     defaultSetSwitchValue("#is_external", 'Y');
        // }else{
        //     defaultSetSwitchValue("#is_internal", 'N');
        //     defaultSetSwitchValue("#is_external", 'N');
        // }

        $('#enrollNewCourseModal').find('.enrollCourseTab').removeClass('active');
        $('#enrollNewCourseModal').find('.enrollCourseTab:first').addClass('active');
        $('#enrollNewCourseModal')
            .find('.enrollNewCourseModal-wrap')
            .animate({ scrollTop: 0 }, 500);

        // Hide AVITMISS claim related
        $(document).find('.claimRelatedDiv').hide();
        $('#pci').attr('required', false);
        $('#pcsi').attr('required', false);
        $('#asso_course_identi').attr('required', false);

        // getDropdown("offer_id_add","",'get-offer-id-list',{'student_id': studentId, 'new_offer': 'yes'},'Select Offer Id');
        // getDropdown("course_id_add","", 'get-course-list',{'college_id': collegeId},'Select Course');
        // getDropdown("campus_id","", 'get-all-campus-list',{'college_id': collegeId},'Select Campus');
        // getDropdown("res_cal_method"," ", 'get-result-cal-method', "",'Select Result Calculation Method');
        // getDropdown("agent_id","", 'get-agent-list',{'college_id': collegeId},'Select Agent');
        // getDropdown("intake_year", "",'get-constant-data',{'action': 'arrIntakeYear'},'Select Intake Year');
        // getDropdown("course_template", "",'get-course-wise-template-list',{'college_id': collegeId},'Select Course Template');
        // getDropdown("survey_contact_status", "","get-constant-data",{'action': 'arrSurveyContactStatusDiv'},'Select Survey Contact Status');
        // getDropdown("add_status","", 'get-constant-data',{'action': 'arrCourseStatus'},'Select Status');
        // getDropdown("add_study_reason","", 'get-study-reason', {},'Select Study Reason');
        // getDropdownBlank("employer_id", "",'get-employer-name-list',"",'Select Employer')

        getDropdownFromApi('offer_id_add', '', arr.offerIdData, 'Select Offer Id');
        // getDropdownFromApi("course_id_add","", arr.coursesData, 'Select Course');

        function valueTemplate(dataItem) {
            let hasSupersededDate = dataItem.is_superseded
                ? "<div class='flex items-center justify-center px-2.5 py-0.5 bg-red-100 rounded-md'><p class='text-sm leading-4 text-center text-red-500 truncate'>Superseded</p></div>"
                : '';
            return `<div  data-superseded_date_expired_no_format = "${dataItem.superseded_date_expired_no_format}" data-superseded_date_expired="${dataItem.superseded_date_expired}" data-superseded_date="${dataItem.superseded_date}" class="inline-flex space-x-1 items-end justify-between getSupersededDate">${hasSupersededDate}
                <p class="text-sm leading-4">${dataItem.Name}</p></div>`;
        }
        getDropdownBlank('course_id_add', '', '', '', 'Select Course');

        // $("#course_id_add").kendoDropDownList({
        //     filter: "contains",
        //     filterInput: {
        //         width: "100%"
        //     },
        //     valueTemplate: valueTemplate,
        //     template: valueTemplate,
        //     dataTextField: "Name",
        //     dataValueField: "Id",
        //     dataType: "json",
        //     dataSource:  {
        //         schema: { data: 'data' },
        //         data: arr.coursesData
        //     },
        //     value: ''
        // });
        getDropdownFromApi('campus_id_add', '', arr.campusData, 'Select Campus');
        let res_cal_method = $('#res_cal_method').kendoDropDownList({
            optionLabel: 'Select Result Calculation Method',
        });
        res_cal_method.data('kendoDropDownList').readonly();
        getDropdownFromApi('agent_id', '', arr.agentsData, 'Select Agent');

        getDropdownFromApi(
            'course_duration_type',
            arr.student_detail.course_duration_type ?? '2',
            arr.courseDurationType,
            'Select Duration Type'
        );
        getDropdownFromApi('intake_year', '', arr.intakeYearData, 'Select Intake Year');
        getDropdownFromApi('course_template', '', arr.templateData, 'Select Course Template');
        getDropdownFromApi(
            'survey_contact_status',
            '',
            arr.surveyContactData,
            'Select Survey Contact Status'
        );
        getDropdownFromApi('add_status', '', arr.courseStatusData, 'Select Status');
        getDropdownFromApi('add_study_reason', '', arr.studyReasonData, 'Select Study Reason');
        getDropdownFromApi('employer_id', '', arr.employerData, 'Select Employer');
        getDropdownFromApi('subject_clming_for_credit', '', arr.subjectCredit, 'Select Credit');

        getDropdownBlank('intake_date', '', '', '', 'Select Intake Date');
        getDropdownFromApi(
            'add_contract_schedule_id',
            '',
            arr.contractSchedule,
            'Select Contract Schedule'
        );

        setKendoDatePicker('#ec_issued_date', '', dateFormatFrontSideJS);
        setKendoDatePicker('#ec_start_date', '', dateFormatFrontSideJS);
        setKendoDatePicker('#ec_finish_date', '', dateFormatFrontSideJS);
        setKendoDatePicker('#census_date1', '', dateFormatFrontSideJS);
        setKendoDatePicker('#census_date2', '', dateFormatFrontSideJS);
        setKendoDatePicker('#census_date3', '', dateFormatFrontSideJS);

        setKendoTextArea('#application_req', 'Write request...');
        setKendoTextArea('#credit_transfer', 'Write request...');
        setKendoTextArea('#special_instructions', 'Write request...');

        $('#classTypeTab')
            .find('.CensusDateDiv')
            .toggle(arr.student_detail.student_type == 'Domestic');
        $('.defaultContractScheduleSetTab').toggle(arr.student_detail.student_type == 'Domestic');
        $('.subject_clming_for_credit').hide();
        $('.scholarship_percentage').hide();
        var ecFormContainer = $(ecFormId);
        kendo.init(ecFormContainer);
        ecFormContainer.kendoValidator({
            messages: {
                required: function (input) {
                    return input.data('message');
                },
            },
        });
    }
    function setEditEnrollFormInputs(arr) {
        let responseArr = {
            arr: arr.student_detail,
            isScheduleExist: arr.isScheduleExist,
            isValidForExtend: arr.isValidForExtend,
        };
        let ectEditList = [
            'editGeneralDetails',
            'editIntakeDetails',
            'editFeesDetails',
            'editDefaultContractScheduleSet',
            'editclassType',
        ];

        $.each(ectEditList, function (index, tabName) {
            let tempHtml = kendo.template($(`#${tabName}Template`).html())(responseArr);
            $(document).find(`#${tabName}Tab`).html(tempHtml);
        });
        $(document).find('.isHigherEdDiv').toggle(arr.student_detail.isHigherEd);
        if (arr.student_detail.is_claim == 1) {
            $(document).find('.claimRelatedDiv').show();
            $('#edit_pci').attr('required', true);
            $('#edit_pcsi').attr('required', true);
            $('#edit_aci').attr('required', true);
        } else {
            $(document).find('.claimRelatedDiv').hide();
            $('#edit_pci').attr('required', false);
            $('#edit_pcsi').attr('required', false);
            $('#edit_aci').attr('required', false);
        }
        defaultSetSwitchValue('#is_claim_edit', arr.student_detail.is_claim);
        defaultSetSwitchValue('#is_qualification_edit', arr.student_detail.is_qualification);
        defaultSetSwitchValue('#is_certificate_edit', arr.student_detail.is_certificate);
        $('#is_certificate_edit').data('kendoSwitch').enable(false);
        if (arr.isCertificateGenerated > 0) {
            $('.certificateIssueDateEdit').text(arr.certificate_data.date_generated);
            $('.issuedByEdit').text(arr.certificate_data.name);
            console.log('sss');
            $(document).find('#is_certificate_edit').data('kendoSwitch').check(true);
            switchValueManage('#is_certificate_edit', true);
        } else {
            $('.certificateIssueDateEdit').text('-');
            $('.issuedByEdit').text('-');
            console.log('ssss');
            switchValueManage('#is_certificate_edit', false);
            $(document).find('#is_certificate_edit').data('kendoSwitch').check(false);
        }
        defaultSetSwitchValue('#mode_of_delivery_edit', arr.student_detail.mode_of_delivery);
        // defaultSetSwitchValue("#is_finish_dt_edit", arr.student_detail.is_finish_dt);
        defaultSetSwitchValue('#auto_update_to_sru_edit', arr.student_detail.auto_update_to_sru);
        defaultSetSwitchValue('#is_internal_edit', arr.student_detail.internal);
        defaultSetSwitchValue('#is_external_edit', arr.student_detail.external);

        defaultSetSwitchValue(
            '#workplace_based_delivery_edit',
            arr.student_detail.workplace_based_delivery
        );
        if (arr.student_detail.student_type != 'Domestic') {
            $('#is_internal_edit').data('kendoSwitch').enable(false);
            $('#is_external_edit').data('kendoSwitch').enable(false);
            $('#workplace_based_delivery_edit').data('kendoSwitch').enable(false);
        } else {
            $('#is_internal_edit').data('kendoSwitch').enable(true);
            $('#is_external_edit').data('kendoSwitch').enable(true);
            $('#workplace_based_delivery_edit').data('kendoSwitch').enable(true);
        }
        $(eceModalId).find('.editEnrollCourseTab').removeClass('active');
        $(eceModalId).find('.editEnrollCourseTab:first').addClass('active');
        $(eceModalId).find('.enrollEditCourseModal-wrap').animate({ scrollTop: 0 }, 500);

        setKendoDatePicker(
            '#edit_issued_date',
            arr.student_detail.issued_date,
            dateFormatFrontSideJS
        );
        setKendoDatePicker(
            '#edit_start_date',
            arr.student_detail.start_date,
            dateFormatFrontSideJS
        );
        setKendoDatePicker(
            '#edit_finish_date',
            arr.student_detail.finish_date,
            dateFormatFrontSideJS
        );

        $('#editclassTypeTab')
            .find('.EditCensusDateDiv')
            .toggle(arr.student_detail.student_type == 'Domestic');
        $('.editDefaultContractScheduleSetTab').toggle(
            arr.student_detail.student_type == 'Domestic'
        );
        if (arr.student_detail.student_type == 'Domestic') {
            setKendoDatePicker(
                '#edit_census_date1',
                arr.student_detail.census_date1,
                dateFormatFrontSideJS
            );
            setKendoDatePicker(
                '#edit_census_date2',
                arr.student_detail.census_date2,
                dateFormatFrontSideJS
            );
            setKendoDatePicker(
                '#edit_census_date3',
                arr.student_detail.census_date3,
                dateFormatFrontSideJS
            );
        }

        setKendoTextArea(
            '#application_req_edit',
            'Write request...',
            5,
            arr.student_detail.application_request
        );
        setKendoTextArea(
            '#credit_transfer_edit',
            'Write request...',
            5,
            arr.student_detail.credit_transfer_request
        );
        setKendoTextArea(
            '#special_instructions_edit',
            'Write request...',
            5,
            arr.student_detail.special_instructions
        );

        // getDropdown("offer_id_edit",  arr.student_detail.offer_id, 'get-offer-id-list', {'student_id': studentId, 'new_offer': 'no'});
        // getDropdown("course_id_edit", arr.student_detail.course_id, 'get-course-list', {'college_id': collegeId});
        // getDropdown("campus_id_edit", arr.student_detail.campus_id, 'get-campus-list', {'college_id': collegeId});
        // getDropdown("res_cal_method_edit", arr.student_detail.res_cal_method, 'get-result-cal-method');
        // getDropdown("agent_id_edit", arr.student_detail.agent_id, 'get-agent-list', {'college_id': collegeId});
        // getDropdown("edit_intake_year", arr.student_detail.intake_year, 'get-constant-data', {'action': 'arrIntakeYear'});
        // getDropdown("edit_course_template", arr.student_detail.course_template, 'get-course-wise-template-list', {'college_id': collegeId, courseId: arr.student_detail.course_id});
        // getDropdown("edit_survey_contact_status", arr.student_detail.survey_contact_status,"get-constant-data",{'action': 'arrSurveyContactStatusDiv'},'Select Survey Contact Status');
        // getDropdown("edit_status", arr.student_detail.status, 'get-constant-data', {'action': 'arrCourseStatus'});
        // getDropdown("edit_study_reason", arr.student_detail.study_reason_id, 'get-study-reason');
        // getDropdown("edit_employer_id", arr.student_detail.employer_id,'get-employer-name-list',{'college_id': collegeId}, 'Select Employer');

        getDropdownFromApi(
            'offer_id_edit',
            arr.student_detail.offer_id,
            arr.offerIdData,
            'Select Offer Id'
        );
        getDropdownFromApi(
            'course_id_edit',
            arr.student_detail.course_id,
            arr.coursesData,
            'Select Course'
        );
        getDropdownFromApi(
            'campus_id_edit',
            arr.student_detail.campus_id,
            arr.campusData,
            'Select Campus'
        );
        getDropdownFromApi(
            'res_cal_method_edit',
            arr.student_detail.res_cal_method,
            arr.resultData,
            'Select Result Calculation Method'
        );
        $('#res_cal_method_edit').data('kendoDropDownList').readonly();
        getDropdownFromApi(
            'agent_id_edit',
            arr.student_detail.agent_id,
            arr.agentsData,
            'Select Agent'
        );
        getDropdownFromApi(
            'edit_intake_year',
            arr.student_detail.intake_year,
            arr.intakeYearData,
            'Select Intake Year'
        );
        getDropdownFromApi(
            'edit_course_template',
            arr.student_detail.course_template,
            arr.templateData,
            'Select Course Template'
        );
        getDropdownFromApi(
            'edit_survey_contact_status',
            arr.student_detail.survey_contact_status,
            arr.surveyContactData,
            'Select Survey Contact Status'
        );
        getDropdownFromApi(
            'edit_status',
            arr.student_detail.status,
            arr.courseStatusData,
            'Select Status'
        );
        getDropdownFromApi(
            'edit_study_reason',
            '0' + arr.student_detail.study_reason_id,
            arr.studyReasonData,
            'Select Study Reason'
        );
        getDropdownFromApi(
            'edit_employer_id',
            arr.student_detail.employer_id,
            arr.employerData,
            'Select Employer'
        );
        getDropdownFromApi(
            'edit_subject_clming_for_credit',
            arr.student_detail.subject_clming_for_credit,
            arr.subjectCredit,
            'Select Credit'
        );

        getDropdownFromApi(
            'edit_contract_schedule_id',
            arr.student_detail.contract_schedule_id,
            arr.contractSchedule,
            'Select Contract Schedule'
        );
        getDropdownFromApi(
            'edit_course_duration_type',
            arr.student_detail.course_duration_type != ''
                ? arr.student_detail.course_duration_type
                : '2',
            arr.courseDurationType,
            'Select Duration Type'
        );
        setDropdownWithoutAPI(
            '#edit_intake_date',
            'Name',
            'Id',
            arr.student_detail.intakeDateList,
            arr.student_detail.intake_date
        );
        $(document).find('#edit_total_weeks').val(arr.student_detail.total_weeks);
        $(document).find('#edit_enroll_fee').val(arr.student_detail.enroll_fee);
        $(document).find('#edit_course_fee').val(arr.student_detail.course_fee);
        $(document).find('#edit_default_unit_fee').val(arr.student_detail.default_unit_fee);
        $(document).find('#edit_course_upfront_fee').val(arr.student_detail.course_upfront_fee);
        $(document).find('#edit_course_material_fee').val(arr.student_detail.course_material_fee);
        $(document).find('#edit_pci').val(arr.student_detail.purchasing_contract_identifier);
        $(document)
            .find('#edit_pcsi')
            .val(arr.student_detail.purchasing_contract_schedule_identifier);
        $(document).find('#edit_aci').val(arr.student_detail.associated_course_identifier);
        $(document).find('#edit_contract_schedule_id').val(arr.student_detail.contract_schedule_id);
        $(document).find('#edit_pcc').val(arr.student_detail.purchasing_contract_ode);
        $(document).find('#edit_schedule_code').val(arr.student_detail.schedule_code);
        $(document).find('#edit_course_fee').val(arr.student_detail.course_fee);

        setTuitionFees(
            arr.student_detail.course_id,
            arr.student_detail.intake_year,
            arr.student_detail.intake_date
        );
        let fulltimeLearingInputId =
            arr.student_detail.is_fulltimelearing == '1'
                ? '#is_fulltimelearing_edit_1'
                : '#is_fulltimelearing_edit_0';
        let orientationInputId =
            arr.student_detail.is_orientation == '1'
                ? '#is_orientation_edit_yes'
                : '#is_orientation_edit_no';
        $(document).find(fulltimeLearingInputId).trigger('click');
        $(document).find(orientationInputId).trigger('click');
        let isMaterialFeeIncInitailPayment =
            arr.student_detail.is_material_fee_inc_initail_payment == '1'
                ? '#is_material_fee_inc_initail_payment_edit_yes'
                : '#is_material_fee_inc_initail_payment_edit_no';
        $(document).find(isMaterialFeeIncInitailPayment).trigger('click');
        let advanceStandingCredit =
            arr.student_detail.advance_standing_credit == '1'
                ? '#advance_standing_credit_edit_yes'
                : '#advance_standing_credit_edit_no';
        $(document).find(advanceStandingCredit).trigger('click');

        const isReceivingAnyScholarship =
            arr.student_detail.is_receiving_any_scholarship === 'yes'
                ? '#is_receiving_any_scholarship_edit_yes'
                : '#is_receiving_any_scholarship_edit_no';
        $(isReceivingAnyScholarship).prop('checked', true).trigger('change');

        console.log(isReceivingAnyScholarship);
        $(document)
            .find(
                'input[name="wil_requirements"][value="' +
                    arr.student_detail.wil_requirements +
                    '"]'
            )
            .prop('checked', true);
        $(document)
            .find(
                'input[name="third_party_providers"][value="' +
                    arr.student_detail.third_party_providers +
                    '"]'
            )
            .prop('checked', true);

        $(document)
            .find('#edit_scholarship_percentage')
            .val(arr.student_detail.scholarship_percentage);

        var ecEditFormContainer = $(eceFormId);
        kendo.init(ecEditFormContainer);
        ecEditFormContainer.kendoValidator({
            messages: {
                required: function (input) {
                    return input.data('message');
                },
            },
        });
    }

    function appendStartDate() {
        let intakeDate = $('#intake_date').val();
        if (intakeDate != '' && intakeDate != null) {
            setKendoDatePicker('#ec_start_date', intakeDate, dateFormatFrontSideJS);
            getFinishDate(true);
        }
    }
    function appendStartDateEdit() {
        let intakeDate = $('#edit_intake_date').val();
        if (intakeDate != '' && intakeDate != null) {
            setKendoDatePicker('#edit_start_date', intakeDate, dateFormatFrontSideJS);
            getFinishDate();
        }
    }

    function getFinishDate(isEditForm = false) {
        let startDateId = isEditForm ? '#ec_start_date' : '#edit_start_date';
        let finishDateId = isEditForm ? '#ec_finish_date' : '#edit_finish_date';
        let totalWeeksId = isEditForm ? '#total_weeks' : '#edit_total_weeks';
        let durationTypeId = isEditForm ? '#course_duration_type' : '#edit_course_duration_type';

        let dateInput = $(startDateId).val();
        if (!dateInput) return false;

        let totalDuration = parseInt($(totalWeeksId).val());
        if (!totalDuration || totalDuration <= 0) {
            $(finishDateId).val('');
            return false;
        }

        let durationType = $(durationTypeId).val() || '2'; // Default to 'week' (value 2)

        // Calculate finish date based on duration type
        let startDate = parseDate(dateInput);
        let finishDate = calculateFinishDateByType(startDate, totalDuration, durationType);

        if (finishDate) {
            let formattedDate = formatDateForKendo(finishDate);
            $(finishDateId).data('kendoDatePicker').value(formattedDate);
        }

        return false;
    }

    function calculateTotalWeeksFromDates(startDateId, finishDateId, totalWeeksId) {
        let startDateVal = $(startDateId).val();
        let finishDateVal = $(finishDateId).val();

        if (!startDateVal || !finishDateVal) return;

        let startDate = parseDate(startDateVal);
        let finishDate = parseDate(finishDateVal);

        // Get duration type for calculation
        let durationTypeId = '#course_duration_type';
        if (totalWeeksId.includes('edit_')) {
            durationTypeId = '#edit_course_duration_type';
        } else if (totalWeeksId.includes('extend_')) {
            durationTypeId = '#extend_course_duration_type';
        }

        let durationType = $(durationTypeId).val() || '2'; // Default to 'week' (value 2)

        // Calculate duration based on type
        let duration = calculateDurationFromDates(startDate, finishDate, durationType);

        // Update the total duration field
        $(totalWeeksId).val(duration);
    }

    function parseDate(dateStr) {
        if (!dateStr) return null;
        // Handle DD-MM-YYYY format
        if (dateStr.includes('-') && dateStr.split('-')[0].length <= 2) {
            let parts = dateStr.split('-');
            return new Date(parts[2], parts[1] - 1, parts[0]); // year, month (0-indexed), day
        }
        // Handle YYYY-MM-DD format
        else {
            return new Date(dateStr);
        }
    }

    function calculateFinishDateByType(startDate, duration, durationType) {
        if (!startDate || !duration) return null;
        let finishDate = new Date(startDate);
        switch (durationType) {
            case '1': // day
                finishDate.setDate(finishDate.getDate() + duration);
                break;
            case '2': // week (default)
                finishDate.setDate(finishDate.getDate() + duration * 7);
                break;
            case '3': // month
                finishDate.setMonth(finishDate.getMonth() + duration);
                break;
            case '4': // year
                finishDate.setFullYear(finishDate.getFullYear() + duration);
                break;
            default: // default to weeks
                finishDate.setDate(finishDate.getDate() + duration * 7);
                break;
        }
        return finishDate;
    }

    function calculateDurationFromDates(startDate, finishDate, durationType) {
        if (!startDate || !finishDate) return 0;
        let timeDifference = finishDate.getTime() - startDate.getTime();
        let daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));
        switch (durationType) {
            case '1': // day
                return daysDifference;
            case '2': // week (default)
                return Math.ceil(daysDifference / 7);
            case '3': // month
                let monthsDiff = (finishDate.getFullYear() - startDate.getFullYear()) * 12;
                monthsDiff += finishDate.getMonth() - startDate.getMonth();
                return Math.ceil(monthsDiff);
            case '4': // year
                return Math.ceil(finishDate.getFullYear() - startDate.getFullYear());
            default: // default to weeks
                return Math.ceil(daysDifference / 7);
        }
    }

    function formatDateForKendo(date) {
        if (!date) return '';
        let year = date.getFullYear();
        let month = String(date.getMonth() + 1).padStart(2, '0');
        let day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function getCollageEnrolmentFees(courseId, intakeYear = '', startDate = '') {
        let dataArr = {
            courseId: courseId,
            intakeYear: intakeYear,
            startDate: startDate,
        };

        ajaxActionV2('api/get-collage-enrolment-fees', 'POST', dataArr, function (response) {
            let res = response.data;
            $('#enroll_fee').val(res.enrolmentFee);
            $('#course_upfront_fee').val(res.upfrontFee);
            $('#course_material_fee').val(res.materialFee);
            if (typeof res.course[0] != 'undefined') {
                $('#tuition_fee').val(res.course[0].tuition_fee);
                $('#total_weeks').val(res.course[0].course_duration);
                let qFlag = res.course[0].module_delivery == 0 ? true : false;
                $(document).find('#is_qualification').data('kendoSwitch').check(qFlag);
                switchValueManage('#is_qualification', qFlag);
                $(document)
                    .find('#is_qualification_hidden')
                    .val(qFlag ? 1 : 0);
                $(document)
                    .find('#mode_of_delivery')
                    .data('kendoSwitch')
                    .check(res.course[0].module_delivery ? true : false);
            }
            $(document).find('.isHigherEdDiv').toggle(res.course[0].isHigherEd);
            $(document)
                .find('#course_duration_type')
                .data('kendoDropDownList')
                .value(res.course[0].couse_duration_type);
            if (startDate == '') {
                setDropdownWithoutAPI('#intake_date', 'Name', 'Id', res.intakeDateList);
            }
        });
    }
    function getCollageEnrolmentFeesEdit(courseId, intakeYear = '', startDate = '') {
        let dataArr = {
            courseId: courseId,
            intakeYear: intakeYear,
            startDate: startDate,
        };

        ajaxActionV2('api/get-collage-enrolment-fees', 'POST', dataArr, function (response) {
            let res = response.data;
            $('#edit_enroll_fee').val(res.enrolmentFee);
            $('#edit_course_upfront_fee').val(res.upfrontFee);
            $('#edit_course_material_fee').val(res.materialFee);
            if (typeof res.course[0] != 'undefined') {
                $('#edit_tuition_fee').val(res.course[0].tuition_fee);
                $(document)
                    .find('#mode_of_delivery_edit')
                    .data('kendoSwitch')
                    .check(res.course[0].module_delivery ? true : false);
            }
            $(document).find('.isHigherEdDiv').toggle(res.course[0].isHigherEd);
            if (startDate == '') {
                setDropdownWithoutAPI('#edit_intake_date', 'Name', 'Id', res.intakeDateList);
            }
        });
    }

    function getCourseCampus(courseId, intakeYear = '') {
        if (intakeYear == '') {
            getDropdown('campus_id', '', 'get-all-campus-list', {
                college_id: collegeId,
                courseId: courseId,
            });
        } else if (intakeYear != '') {
            getDropdown('campus_id', '', 'get-all-campus-list', {
                college_id: collegeId,
                intakeYear: intakeYear,
            });
        }
    }

    function getResultCalculationMethod(courseId) {
        getDropdownWithoutFilter('res_cal_method', ' ', 'get-result-cal-method', {
            courseId: courseId,
        });
    }

    function getCourseTemplateList(courseId) {
        getDropdown(
            'course_template',
            '',
            'get-course-wise-template-list',
            {
                college_id: collegeId,
                courseId: courseId,
            },
            'Select Course Template'
        );
    }

    // function manageKendoSwitch(formId) {
    //     $(formId + ' :input[type="checkbox"]').each(function (index) {
    //         let tempId = $(this).attr("id");
    //         $("#" + tempId).kendoSwitch({
    //             size: "small",
    //             change: function (e) {
    //                 switchValueManage("#" + tempId, e.checked);
    //                 if (tempId == "is_finish_dt" && e.checked) {
    //                     getFinishDate();
    //                 }
    //                 if (tempId == "is_finish_dt_edit" && e.checked) {
    //                     getFinishDate(true);
    //                 }
    //                 if (
    //                     jQuery.inArray(tempId, [
    //                         "is_claim_edit",
    //                         "is_claim",
    //                     ]) !== -1
    //                 ) {
    //                     if (e.checked) {
    //                         $(document).find(".claimRelatedDiv").show();
    //                         $("#pci").attr("required", true);
    //                         $("#pcsi").attr("required", true);
    //                         $("#asso_course_identi").attr("required", true);
    //                     } else {
    //                         $(document).find(".claimRelatedDiv").hide();
    //                         $("#pci").attr("required", false);
    //                         $("#pcsi").attr("required", false);
    //                         $("#asso_course_identi").attr("required", false);
    //                     }
    //                 }
    //             },
    //         });
    //     });
    // }

    function defaultSetSwitchValue(inputId, val) {
        let switchValue = jQuery.inArray(val, [1, '1', 'Y']) != -1 ? true : false;
        $(inputId).kendoSwitch({
            size: 'medium',
            change: function (e) {
                switchValueManage(inputId, e.checked);
                // if(tempId == 'is_finish_dt' && e.checked){
                //     getFinishDate();
                // }
                // if (inputId == "#is_finish_dt_edit" && e.checked) {
                //     getFinishDate(true);
                // }
                if (inputId == '#is_claim_edit' || inputId == '#is_claim') {
                    if (e.checked) {
                        $(document).find('.claimRelatedDiv').show();
                        $('#edit_pci').attr('required', true);
                        $('#edit_pcsi').attr('required', true);
                        $('#edit_aci').attr('required', true);
                    } else {
                        $(document).find('.claimRelatedDiv').hide();
                        $('#edit_pci').attr('required', false);
                        $('#edit_pcsi').attr('required', false);
                        $('#edit_aci').attr('required', false);
                    }
                }
            },
        });
        $(document).find(inputId).data('kendoSwitch').check(switchValue);
        switchValueManage(inputId, switchValue);
    }

    function switchValueManage(inputId, switchValue) {
        $(document)
            .find(inputId)
            .closest('.customSwitchButton')
            .siblings('div')
            .find('.switchText')
            .text(switchValue ? 'Yes' : 'No');
    }

    function manageSliderWidth(modalId) {
        let enrollNewCourse = $(document).find(modalId);
        var wwidth = $(document).width();
        var sswidth = $('.student-profile-left-sidebar').width();
        var swidth = $('#sidebar').width() + 16 + sswidth;
        var twidth = wwidth - swidth;
        var windoWidth = '';
        var windoLeft = '';
        windoWidth = twidth + 'px';
        windoLeft = swidth + 'px';
        enrollNewCourse.getKendoWindow().setOptions({
            width: windoWidth,
            position: { left: windoLeft, top: '0' },
        });
        enrollNewCourse.getKendoWindow().open();
        enrollNewCourse
            .parent('div')
            .find('.k-window-titlebar')
            .addClass(
                'titlebar-modal titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500'
            )
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    }

    function setTuitionFees(courseId, intakeYear = '', startDate = '') {
        let dataArr = {
            courseId: courseId,
            intakeYear: intakeYear,
            startDate: startDate,
        };
        ajaxActionV2('api/get-collage-enrolment-fees', 'POST', dataArr, function (response) {
            $('#edit_tuition_fee').val(response.data.course[0].tuition_fee);
        });
    }
    $('#courseIsSupersededModel').kendoDialog({
        width: '560px',
        title: 'Course Superseded',
        content:
            "This course has been superseded on <span class='superseded_date'>19/Jul/2021</span> and will expire on <span class='superseded_date_expire'>19/Jul/2022</span>. Do you still want to enroll the student in this course?",
        actions: [
            {
                text: 'No',
                action: function () {
                    courseIsSupersededModelAction(false);
                },
            },
            {
                text: 'Yes Enroll',
                primary: true,
                action: function () {
                    courseIsSupersededModelAction(true);
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog('#courseIsSupersededModel'),
        visible: false,
    });
    $('#courseIsSupersededExpiredModel').kendoDialog({
        width: '560px',
        title: 'Course Expired',
        content:
            'This course may not be allowed to be delivered in your institution as the course has expired. Please confirm with your supervisor to proceed.',
        actions: [
            {
                text: 'No',
                action: function () {
                    courseIsSupersededExpiredModelAction(false);
                },
            },
            {
                text: 'Proceed',
                primary: true,
                action: function () {
                    courseIsSupersededExpiredModelAction(true);
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog('#courseIsSupersededExpiredModel'),
        visible: false,
    });
    function courseIsSupersededModelAction(buttonValue) {
        if (buttonValue) {
            $('#courseIsSupersededModel').data('kendoDialog').close();
        } else {
            $('#courseIsSupersededModel').data('kendoDialog').close();
            closeKendoWindow(ecModalId);
        }
    }
    function courseIsSupersededExpiredModelAction(buttonValue) {
        if (buttonValue) {
            $('#courseIsSupersededExpiredModel').data('kendoDialog').close();
        } else {
            $('#courseIsSupersededExpiredModel').data('kendoDialog').close();
            closeKendoWindow(ecModalId);
        }
    }

    $('body').on('change', "input[name='advance_standing_credit']", function () {
        const subjectSection = $('.subject_clming_for_credit');
        const inputs = subjectSection.find('input, select, textarea');
        const isChecked = $('[name="advance_standing_credit"]:checked').val() === '1';

        subjectSection.toggle(isChecked);
        inputs.prop('required', isChecked);
    });

    $('body').on('change', "input[name='is_receiving_any_scholarship']", function () {
        const scholarshipSection = $('.scholarship_percentage');
        const inputs = scholarshipSection.find('input, select, textarea');
        const isChecked = $('[name="is_receiving_any_scholarship"]:checked').val() === '1';

        scholarshipSection.toggle(isChecked);
        inputs.prop('required', isChecked);
    });

    window.getDurationTypeText = function (durationType) {
        const durationTypes = {
            1: 'Day',
            2: 'Week',
            3: 'Month',
            4: 'Year',
        };
        return durationTypes[durationType] || 'Week';
    };

    function setUpCourseExtensionForm(courseData) {
        setKendoDatePicker('#extend_finish_date', courseData.finish_date, dateFormatFrontSideJS);

        // Store the original finish date for comparison
        $('#extend_start_date').data('original-finish-date', courseData.finish_date);

        // Initialize form validation
        let extendFormContainer = $('#studentCourseExtendForm');
        kendo.init(extendFormContainer);
        extendFormContainer.kendoValidator({
            messages: {
                required: function (input) {
                    return input.data('message') || 'This field is required';
                },
            },
        });
    }

    function openCourseExtensionConfirmDialog() {
        // Create confirmation dialog if it doesn't exist
        if ($('#courseExtensionConfirmDialog').length === 0) {
            $('body').append('<div id="courseExtensionConfirmDialog"></div>');

            $('#courseExtensionConfirmDialog').kendoDialog({
                width: '400px',
                title: 'Confirm Course Extension',
                content:
                    'Are you sure you want to extend the course due date? The study duration will be recalculated and may increase. Do you want to proceed?',
                actions: [
                    {
                        text: 'Cancel',
                        action: function () {
                            return true;
                        }, // Close dialog
                    },
                    {
                        text: 'Yes, Extend Course',
                        primary: true,
                        action: function () {
                            saveCourseExtension();
                            return true; // Close dialog
                        },
                    },
                ],
                animation: defaultOpenAnimation(),
                visible: false,
            });
        }

        // Open the confirmation dialog
        $('#courseExtensionConfirmDialog').data('kendoDialog').open();
    }

    function saveCourseExtension() {
        let extensionData = {
            college_id: collegeId,
            student_id: studentId,
            student_course_id: selectedStudCourseID,

            //start_date: $('#extend_start_date').val(),
            //finish_date: $('#extend_finish_date').val(),
            //duration_type: $('#extend_course_duration_type').val(),
            //total_weeks: $('#extend_total_weeks').val(),
            //extension_reason: $('#extension_reason').val(),
        };
        let formData = getSerializeFormArray('#studentCourseExtendForm', extensionData);
        ajaxActionV2('api/extend-course-due-date', 'POST', formData, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                updateIntakeDetailsAfterExtension(response.data);
                closeKendoWindow('#studentCourseExtendModal');

                // Refresh the course tab data to show updated information
                updateStudentCourseTabData();
            }
        });
    }

    function updateIntakeDetailsAfterExtension(apiResponseData) {
        try {
            // API response contains: finish_date (Y-m-d), total_weeks, extension_reason

            // Update finish date in the intake details form
            if ($('#edit_finish_date').length > 0 && apiResponseData.finish_date) {
                let finishDatePicker = $('#edit_finish_date').data('kendoDatePicker');
                if (finishDatePicker) {
                    // Convert Y-m-d format to Date object for Kendo DatePicker
                    let finishDate = new Date(apiResponseData.finish_date);
                    if (finishDate && !isNaN(finishDate.getTime())) {
                        finishDatePicker.value(finishDate);
                    }
                } else {
                    // For regular input, convert to d/m/Y format
                    let displayDate = formatDateForDisplay(apiResponseData.finish_date);
                    $('#edit_finish_date').val(displayDate);
                }
            }

            // Update total weeks in the intake details form
            if ($('#edit_total_weeks').length > 0 && apiResponseData.total_weeks) {
                $('#edit_total_weeks').val(apiResponseData.total_weeks);
            }

            if (apiResponseData.finish_date) {
                let comparisonDate = formatDateForDisplay(apiResponseData.finish_date);
                $('#extend_start_date').data('original-finish-date', comparisonDate);
            }
        } catch (error) {
            console.error('Error updating intake details after extension:', error);
        }
    }

    function formatDateForDisplay(dateStr) {
        try {
            let date;
            if (dateStr.includes('-') && dateStr.split('-')[0].length === 4) {
                date = new Date(dateStr); // Y-m-d format
            } else {
                date = parseDate(dateStr); // Use parseDate for other formats
            }

            if (date && !isNaN(date.getTime())) {
                let day = String(date.getDate()).padStart(2, '0');
                let month = String(date.getMonth() + 1).padStart(2, '0');
                let year = date.getFullYear();
                return `${day}-${month}-${year}`;
            }
            return dateStr;
        } catch (error) {
            console.error('Error formatting date for display:', error);
            return dateStr;
        }
    }

    // Global function for Kendo template to convert duration type value to text
    window.getDurationTypeText = function (durationType) {
        const durationTypes = {
            1: 'Day',
            2: 'Week',
            3: 'Month',
            4: 'Year',
        };
        return durationTypes[durationType] || 'Week';
    };
});
