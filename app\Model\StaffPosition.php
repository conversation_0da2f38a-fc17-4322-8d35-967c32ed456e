<?php

namespace App\Model;

use Carbon;
use Illuminate\Database\Eloquent\Model;

class StaffPosition extends Model
{
    protected $table = 'rto_staff_position';

    protected $fillable = ['id', 'position'];

    public function getStaffPosition()
    {
        return StaffPosition::select('rto_staff_position.*')->get();
    }

    public function getArrStaffPosition()
    {
        $arrPosition = [];
        $positionArray = StaffPosition::select('rto_staff_position.id', 'rto_staff_position.position')->get();
        foreach ($positionArray as $value) {
            $arrPosition[$value->id] = $value->position;
        }

        return $arrPosition;
    }

    public function addPosition($position)
    {
        $objStaffPosition = new StaffPosition;
        $objStaffPosition->position = ucfirst($position);
        $objStaffPosition->save();
        if ($objStaffPosition->save()) {
            return StaffPosition::select('rto_staff_position.*')->get();
        }
    }

    public function getPositionDelete($id)
    {
        StaffPosition::Where('id', $id)->delete();

        return StaffPosition::select('rto_staff_position.*')->get();
    }

    public function getUpdatedAtAttribute($value)
    {

        $y = date('Y', strtotime($value));
        $m = date('m', strtotime($value));
        $d = date('d', strtotime($value));
        $h = date('h', strtotime($value));

        return Carbon::create($y, $m, $d, $h)->diffForHumans(['options' => 0]); // 1 month ago;
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }
}
