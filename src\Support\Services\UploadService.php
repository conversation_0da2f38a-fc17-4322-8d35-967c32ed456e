<?php

namespace Support\Services;

use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Support\DTO\MailDataAttachment;

class UploadService
{
    protected static function disk()
    {
        return Storage::disk(config('filesystems.upload_disk'));
    }

    public static function upload(string $path, File|UploadedFile|string $file, array $options = []): string
    {
        return self::disk()->put($path, $file, $options);
    }

    public static function uploadAs(string $path, $uploadedPath, $filename, $visibility = 'public'): string
    {
        $path = rtrim($path, '/\\');

        return self::disk()->putFileAs($path, $uploadedPath, $filename);
    }

    public static function url(string $path): string
    {
        if (config('filesystems.upload_disk') == 'uploads_s3') {
            return self::disk()->temporaryUrl($path, now()->addMinutes(5));
        }

        // For local storage, use the public URL
        return url($path);
    }

    public static function preview(string $path): string
    {
        $path = ltrim($path, '/');

        return route('preview-files', $path);
    }

    public static function download(string $path, ?string $downloadName = null): string
    {
        $path = ltrim($path, '/');

        // Add download name in query string if provided
        return route('download-files', ['path' => $path]).($downloadName ? ('?name='.urlencode($downloadName)) : '');
    }

    public static function getFilesystem()
    {
        return self::disk();
    }

    public static function delete(string $path): bool
    {
        return self::disk()->delete($path);
    }

    public static function rename(string $from, string $to): bool
    {
        return self::disk()->move($from, $to);
    }

    public static function exists($path): bool
    {
        $disk = config('filesystems.upload_disk');

        if ($disk === 'uploads_s3') {
            return self::disk()->exists($path);
        }

        return false;
    }

    public static function inlineAttachment(string $path, ?string $name = null, ?string $contentId = null): MailDataAttachment
    {
        $disk = self::disk();
        if (! $disk->exists($path)) {
            throw new \Exception("File not found at {$path}");
        }

        $stream = $disk->readStream($path);

        return MailDataAttachment::LazyFromArray(
            [
                'data' => stream_get_contents($stream),
                'name' => $name ?? basename($path),
                'contentId' => $contentId ?? uniqid('cid', true),
                'mime' => $disk->mimeType($path),
            ]

        );
    }

    public static function stream(string $path, ?string $downloadName = null)
    {
        $disk = self::disk();
        if (! $disk->exists($path)) {
            // if(request()->expectsJson() || request)
            // return response()->json(['message' => 'File not found'], 404);
            throw new FileNotFoundException('File not found');
        }

        // Open a read stream for the file
        $stream = $disk->readStream($path);

        // Get the MIME type of the file
        $mime = $disk->mimeType($path);

        $downloadName ??= basename($path);

        // Stream the file content to the response
        return Response::stream(function () use ($stream) {
            fpassthru($stream);
        }, 200, [
            'Content-Type' => $mime,
            'Content-Disposition' => 'inline; filename="'.$downloadName.'"',
        ]);
    }

    public static function streamDownload(string $path, ?string $downloadName = null)
    {
        $disk = self::disk();
        if (! $disk->exists($path)) {
            // if(request()->expectsJson() || request)
            // return response()->json(['message' => 'File not found'], 404);
            throw new FileNotFoundException('File not found');
        }

        // Get the MIME type of the file
        $mime = $disk->mimeType($path);

        $downloadName = $downloadName ?? basename($path);

        // Stream the file content to the response
        return Response::streamDownload(
            fn () => print ($disk->get($path)),
            $downloadName,
            [
                'Content-Type' => $mime,
            ]
        );
    }

    public static function imageEmbed(string $path, array $allowedMimes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', '']): ?string
    {

        $disk = self::disk();
        $base64Image = null;

        $fileExists = $disk->exists($path);
        $mimeType = $fileExists ? $disk->mimeType($path) : null;
        $isValidMime = $mimeType !== null && in_array($mimeType, $allowedMimes, true);
        if ($fileExists && $isValidMime) {
            $stream = $disk->readStream($path);

            if ($stream) {
                $contents = stream_get_contents($stream);
                fclose($stream);

                if ($contents !== false) {
                    $base64Image = 'data:'.$mimeType.';base64,'.base64_encode($contents);
                }
            }
        }

        return $base64Image;
    }

    public static function cacheItem(string $path, ?string $key = null, int $minutes = 1440): ?string
    {
        $cacheKey = $key ? tenant('id').$key : 'file_cache_'.md5($path);

        return Cache::driver('file')->remember(
            $cacheKey,
            $minutes,
            function () use ($path, $cacheKey) {
                $disk = self::disk();

                if (! $disk->exists($path)) {
                    return null;
                }

                try {
                    $mimeType = $disk->mimeType($path);
                    $stream = $disk->readStream($path);

                    if ($stream) {
                        $contents = stream_get_contents($stream);
                        fclose($stream);

                        if ($contents !== false) {
                            // For images, return base64 encoded data
                            if (str_starts_with($mimeType, 'image/')) {
                                return 'data:'.$mimeType.';base64,'.base64_encode($contents);
                            }

                            // For other file types, just return the contents
                            return $contents;
                        }
                    }
                } catch (\Exception $e) {
                    \Log::error('Error caching file: '.$e->getMessage(), [
                        'path' => $path,
                        'key' => $cacheKey,
                    ]);
                }

                return null;
            }
        );
    }

    public static function getFileContent(
        string $path,
    ) {
        $disk = self::disk();
        if (! $disk->exists($path)) {
            throw new FileNotFoundException('File not found at '.$path);
        }

        try {
            $stream = $disk->readStream($path);
            if ($stream) {
                $contents = stream_get_contents($stream);
                fclose($stream);

                if ($contents !== false) {
                    return $contents;
                }
            }
        } catch (\Exception $e) {
            throw new \Exception('Error reading file: '.$e->getMessage());
        }

        return null;
    }
}
