<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :class="labelClass"
            :editor-id="id"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
            <div v-if="hint && hintOnLabel" class="font-light italic text-gray-400">
                ({{ hint }})
            </div>
            <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
        </klabel>
        <div :class="wrapClass">
            <kinput
                :valid="valid"
                :id="id"
                :value="value"
                :default-value="defaultValue"
                :disabled="disabled"
                :placeholder="placeholder"
                :name="name"
                :class="inputClass"
                :type="type"
                @change="handleUpdate"
                @input="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
            />
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else-if="!showValidationMessage && !hintOnLabel">{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { Input } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';
export default {
    props: {
        modelValue: [String, Number],
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        hintOnLabel: { type: Boolean, default: false },
        type: { type: String, default: 'text' },
        id: String,
        valid: Boolean,
        class: String,
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        value: {
            type: [String, Number],
            default: '',
        },
        defaultValue: String,
        name: String,
        pt: {
            type: Object,
            default: {},
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        kinput: Input,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        rootClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('tw-form__fieldwrapper field-horizontal', this.pt.root);
            }
            return twMerge('tw-form__fieldwrapper', this.pt.root);
        },
        wrapClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('k-form-field-wrap', this.pt.wrap);
            }
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            if (this.orientation == 'horizontal') {
                return twMerge(
                    'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                    this.pt.label
                );
            }
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
        inputClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
    },
    emits: ['input', 'change', 'blur', 'focus', 'update:modelValue'],
    methods: {
        handleChange(e) {
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('change', e);
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
        handleUpdate(e) {
            //console.log(e.value);
        },
    },
};
</script>
