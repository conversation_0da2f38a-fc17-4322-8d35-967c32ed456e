<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :class="[(isCheckFirst = 'order-2'), 'k-form-label !mb-0', labelClass]"
            :editor-id="id"
            :editor-valid="valid"
            :optional="optional"
        >
            {{ label }}
        </klabel>
        <div :class="wrapClass">
            <span>
                <checkbox
                    :value="value"
                    :valid="valid"
                    :id="id"
                    @change="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                />
            </span>
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else>{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { Checkbox } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: [String, Number, Boolean],
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        valid: <PERSON><PERSON><PERSON>,
        disabled: Boolean,
        optional: <PERSON><PERSON>an,
        visited: <PERSON><PERSON><PERSON>,
        modified: Boolean,
        isCheckFirst: Boolean,
        value: {
            type: [Boolean, String],
            default: false,
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
        'update:modelValue': null,
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        checkbox: Checkbox,
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
        rootClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('tw-form__fieldwrapper field-horizontal', this.pt.root);
            }
            return twMerge('tw-form__fieldwrapper', this.pt.root);
        },
        wrapClass() {
            if (this.orientation == 'horizontal') {
                return twMerge('k-form-field-wrap', this.pt.wrap);
            }
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            if (this.orientation == 'horizontal') {
                return twMerge(
                    'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                    this.pt.label
                );
            }
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
        inputClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
    },
    methods: {
        handleChange(e) {
            this.$emit('change', e);
            this.$emit('update:modelValue', e.value);
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
